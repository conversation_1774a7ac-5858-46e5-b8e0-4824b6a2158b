import axios from '../api/axios';

/**
 * Get all expenses for the current consultant
 * @returns {Promise} Promise object representing the expenses
 */
export const getExpenses = async () => {
  try {
    const response = await axios.get('/v1/expenses');
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get a specific expense by ID
 * @param {string} expenseId - The expense ID
 * @returns {Promise} Promise object representing the expense
 */
export const getExpenseById = async (expenseId) => {
  try {
    const response = await axios.get(`/v1/expenses/${expenseId}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Create a new expense
 * @param {Object} expenseData - The expense data
 * @returns {Promise} Promise object representing the created expense
 */
export const createExpense = async (expenseData) => {
  console.log('Expense data:', expenseData);
  try {
    // Create a copy of the data without the receipt
    const { receipt, ...expenseDataWithoutReceipt } = expenseData;

    // If there's a receipt file, use FormData
    if (receipt) {
      const formData = new FormData();

      // Add the receipt file
      formData.append('receipt', receipt);

      // Add all other expense data
      Object.entries(expenseDataWithoutReceipt).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      });

      console.log('Form data with receipt:', formData);

      const response = await axios.post('/v1/expenses', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } else {
      // No receipt, send as JSON
      console.log('Sending as JSON:', expenseDataWithoutReceipt);
      const response = await axios.post(
        '/v1/expenses',
        expenseDataWithoutReceipt
      );
      return response.data;
    }
  } catch (error) {
    throw error;
  }
};

/**
 * Update an existing expense
 * @param {string} expenseId - The expense ID
 * @param {Object} expenseData - The updated expense data
 * @returns {Promise} Promise object representing the updated expense
 */
export const updateExpense = async (expenseId, expenseData) => {
  try {
    // Create a copy of the data without the receipt
    const { receipt, ...expenseDataWithoutReceipt } = expenseData;

    // If there's a receipt file, use FormData
    if (receipt) {
      const formData = new FormData();

      // Add the receipt file
      formData.append('receipt', receipt);

      // Add all other expense data
      Object.entries(expenseDataWithoutReceipt).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      });

      const response = await axios.put(`/v1/expenses/${expenseId}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } else {
      // No receipt, send as JSON
      const response = await axios.put(
        `/v1/expenses/${expenseId}`,
        expenseDataWithoutReceipt
      );
      return response.data;
    }
  } catch (error) {
    throw error;
  }
};

/**
 * Delete an expense
 * @param {string} expenseId - The expense ID
 * @returns {Promise} Promise object representing the operation result
 */
export const deleteExpense = async (expenseId) => {
  try {
    const response = await axios.delete(`/v1/expenses/${expenseId}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Upload a receipt for an expense
 * @param {string} id - The expense ID
 * @param {File} file - The receipt file
 * @returns {Promise} Promise object representing the operation result
 */
export const uploadReceipt = async (id, file) => {
  try {
    const formData = new FormData();
    formData.append('receipt', file);

    const response = await axios.post(`/v1/expenses/${id}/receipt`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Create a new expense with receipt in a single request
 * @param {Object} expenseData - The expense data
 * @param {File} receiptFile - The receipt file
 * @returns {Promise} Promise object representing the created expense
 */
export const createExpenseWithReceipt = async (expenseData, receiptFile) => {
  console.log('Creating expense with receipt:', expenseData, receiptFile);
  try {
    // Create a new FormData instance
    const formData = new FormData();

    // Debug the input data
    console.log('Input data type check:');
    console.log('date:', expenseData.date, typeof expenseData.date);
    console.log(
      'description:',
      expenseData.description,
      typeof expenseData.description
    );
    console.log('amount:', expenseData.amount, typeof expenseData.amount);
    console.log('currency:', expenseData.currency, typeof expenseData.currency);
    console.log('category:', expenseData.category, typeof expenseData.category);
    console.log(
      'receipt:',
      receiptFile,
      receiptFile ? receiptFile.name : 'No file'
    );

    // Manually append each field as string to ensure proper serialization
    formData.append('date', String(expenseData.date));
    formData.append('description', String(expenseData.description));
    formData.append('amount', String(expenseData.amount));
    formData.append('currency', String(expenseData.currency));
    formData.append('category', String(expenseData.category));

    // Add converted amount if available
    if (expenseData.convertedAmount) {
      formData.append('convertedAmount', String(expenseData.convertedAmount));
    }

    // Append receipt file if provided
    if (receiptFile) {
      formData.append('receipt', receiptFile, receiptFile.name);
    }

    // Log FormData entries for debugging
    console.log('FormData entries:');
    let hasEntries = false;
    for (let pair of formData.entries()) {
      console.log(
        pair[0] +
          ': ' +
          (pair[0] === 'receipt' ? 'File: ' + pair[1].name : pair[1])
      );
      hasEntries = true;
    }

    if (!hasEntries) {
      console.error('WARNING: FormData is empty!');
    }

    console.log('Sending FormData to API...');

    // Try with direct data if FormData is not working
    const response = await axios.post('/v1/expenses', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    console.log('API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in createExpenseWithReceipt:', error);
    throw error;
  }
};
