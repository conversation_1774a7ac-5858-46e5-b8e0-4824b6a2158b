import axios from 'axios';

const instance = axios.create({
  baseURL: 'https://staging.api.erp.fsli-group.com/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

export default instance;

instance.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    const roles = localStorage.getItem('roles');

    if (token || roles) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  });
  