import { useState } from "react";
import { Calendar, DollarSign, Tag, Receipt, PlusCircle } from "lucide-react";

function Expenses() {
  const [expenseData, setExpenseData] = useState({
    date: "",
    description: "",
    amount: "",
    currency: "EUR",
    category: "",
  });

  const [expenses, setExpenses] = useState([]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setExpenseData({ ...expenseData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setExpenses([...expenses, expenseData]);
    setExpenseData({
      date: "",
      description: "",
      amount: "",
      currency: "EUR",
      category: "",
    });
  };

  return (
    <div className="md:ml-64 p-4 sm:p-6 bg-gray-100 min-h-screen">
      <div className="max-w-xl mx-auto">
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 sm:p-6 rounded-t-xl shadow-md">
          <h2 className="text-xl sm:text-2xl font-bold mb-1 flex items-center">
            <Receipt className="mr-2" size={24} />
            Déclaration de frais
          </h2>
          <p className="text-blue-100 text-sm">Enregistrez et gérez vos dépenses professionnelles</p>
        </div>

        {/* Formulaire */}
        <form onSubmit={handleSubmit} className="bg-white p-4 sm:p-6 rounded-b-xl shadow-md mb-6">
          <div className="space-y-4">
            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <Calendar className="mr-2" size={16} />
                Date
              </label>
              <input
                type="date"
                name="date"
                value={expenseData.date}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500  focus:border-blue-500 outline-none transition-all"
                required
              />
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700">Description</label>
              <input
                type="text"
                name="description"
                value={expenseData.description}
                onChange={handleChange}
                placeholder="Ex : Déjeuner client, billet train..."
                className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                required
              />
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <DollarSign className="mr-2" size={16} />
                Montant
              </label>
              <input
                type="number"
                name="amount"
                value={expenseData.amount}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                min="0"
                required
              />
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700">Devise</label>
              <select
                name="currency"
                value={expenseData.currency}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
              >
                <option value="EUR">€ EUR</option>
                <option value="USD">$ USD</option>
                <option value="CHF">CHF</option>
              </select>
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <Tag className="mr-2" size={16} />
                Catégorie
              </label>
              <select
                name="category"
                value={expenseData.category}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                required
              >
                <option value="">-- Sélectionner --</option>
                <option value="Transport">Transport</option>
                <option value="Hébergement">Hébergement</option>
                <option value="Restauration">Restauration</option>
                <option value="Autre">Autre</option>
              </select>
            </div>

            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors w-full sm:w-auto flex items-center justify-center"
            >
              <PlusCircle className="mr-2" size={18} />
              Enregistrer la dépense
            </button>
          </div>
        </form>

        {/* Liste des dépenses */}
        {expenses.length > 0 && (
          <div className="bg-white p-4 sm:p-6 rounded-xl shadow-md">
            <h3 className="text-lg sm:text-xl font-semibold mb-4 text-gray-800 border-b pb-2">
              Dépenses enregistrées
            </h3>
            <div className="space-y-3">
              {expenses.map((exp, index) => (
                <div
                  key={index}
                  className="border border-gray-200 p-4 rounded-lg bg-gray-50 hover:bg-blue-50 transition-colors"
                >
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    <p className="flex items-center text-gray-700">
                      <Calendar className="mr-2 text-blue-600" size={16} />
                      <span className="font-medium">Date:</span>
                      <span className="ml-1">{exp.date}</span>
                    </p>
                    <p className="flex items-center text-gray-700">
                      <span className="font-medium">Description:</span>
                      <span className="ml-1">{exp.description}</span>
                    </p>
                    <p className="flex items-center text-gray-700">
                      <DollarSign className="mr-2 text-blue-600" size={16} />
                      <span className="font-medium">Montant:</span>
                      <span className="ml-1">{exp.amount} {exp.currency}</span>
                    </p>
                    <p className="flex items-center text-gray-700">
                      <Tag className="mr-2 text-blue-600" size={16} />
                      <span className="font-medium">Catégorie:</span>
                      <span className="ml-1">{exp.category}</span>
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Expenses;

import { useState, useEffect } from "react";
import { Calendar, DollarSign, Tag, Receipt, PlusCircle } from "lucide-react";

function Expenses() {
  const [expenseData, setExpenseData] = useState({
    date: "",
    description: "",
    amount: "",
    currency: "EUR",
    category: "",
  });

  const [convertedAmount, setConvertedAmount] = useState(null);
  const [exchangeRate, setExchangeRate] = useState(null);
  const [expenses, setExpenses] = useState([]);

  const handleChange = async (e) => {
    const { name, value } = e.target;
    const newData = { ...expenseData, [name]: value };
    setExpenseData(newData);

    // Recalculer taux/conversion si "currency" ou "amount" change
    if (name === "currency" || name === "amount") {
      if (newData.currency !== "CHF" && newData.amount > 0) {
        try {
          const res = await fetch(`https://api.frankfurter.app/latest?amount=${newData.amount}&from=${newData.currency}&to=CHF`);
          const data = await res.json();
          setConvertedAmount(data.rates.CHF.toFixed(2));
          setExchangeRate(data.rates.CHF);
        } catch (error) {
          console.error("Erreur de conversion :", error);
          setConvertedAmount(null);
          setExchangeRate(null);
        }
      } else {
        setConvertedAmount(null);
        setExchangeRate(null);
      }
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setExpenses([
    ...expenses,
    {
      ...expenseData,
      convertedCHF: convertedAmount, // 👈 On ajoute ici le montant converti
    },
  ]);
    setExpenseData({
      date: "",
      description: "",
      amount: "",
      currency: "EUR",
      category: "",
    });
    setConvertedAmount(null);
    setExchangeRate(null);
  };

  return (
    <div className="md:ml-64 p-4 sm:p-6 bg-gray-100 min-h-screen">
      <div className="max-w-xl mx-auto">
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 sm:p-6 rounded-t-xl shadow-md">
          <h2 className="text-xl sm:text-2xl font-bold mb-1 flex items-center">
            <Receipt className="mr-2" size={24} />
            Déclaration de frais
          </h2>
          <p className="text-blue-100 text-sm">Enregistrez et gérez vos dépenses professionnelles</p>
        </div>

        {/* Formulaire */}
        <form onSubmit={handleSubmit} className="bg-white p-4 sm:p-6 rounded-b-xl shadow-md mb-6">
          <div className="space-y-4">
            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <Calendar className="mr-2" size={16} />
                Date
              </label>
              <input
                type="date"
                name="date"
                value={expenseData.date}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
                required
              />
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700">Description</label>
              <textarea
                name="description"
                value={expenseData.description}
                onChange={(e) => {
                  handleChange(e);
                }}
                placeholder="Ex : Déjeuner client, billet train..."
                className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all resize-none overflow-hidden min-h-12"
                rows={3}
                required
              />
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <DollarSign className="mr-2" size={16} />
                Montant
              </label>
              <input
                type="number"
                name="amount"
                value={expenseData.amount}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
                min="0"
                required
              />
              {convertedAmount && (
                <p className="text-sm text-green-600 mt-1">
                  ≈ {convertedAmount} CHF (Taux : {exchangeRate?.toFixed(4)})
                </p>
              )}
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700">Devise</label>
              <select
                name="currency"
                value={expenseData.currency}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
              >
                <option value="EUR">€ EUR</option>
                <option value="USD">$ USD</option>
                <option value="CHF">CHF</option>
              </select>
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <Tag className="mr-2" size={16} />
                Catégorie
              </label>
              <select
                name="category"
                value={expenseData.category}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
                required
              >
                <option value="">-- Sélectionner --</option>
                <option value="Transport">Transport</option>
                <option value="Hébergement">Hébergement</option>
                <option value="Restauration">Restauration</option>
                <option value="Autre">Autre</option>
              </select>
            </div>

            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors w-full sm:w-auto flex items-center justify-center"
            >
              <PlusCircle className="mr-2" size={18} />
              Enregistrer la dépense
            </button>
          </div>
        </form>

        {/* Liste des dépenses */}
        {expenses.length > 0 && (
          <div className="bg-white p-4 sm:p-6 rounded-xl shadow-md">
            <h3 className="text-lg sm:text-xl font-semibold mb-4 text-gray-800 border-b pb-2">
              Dépenses enregistrées
            </h3>
            <div className="space-y-3">
              {expenses.map((exp, index) => (
                <div
                  key={index}
                  className="border border-gray-200 p-4 rounded-lg bg-gray-50 hover:bg-blue-50 transition-colors"
                >
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    <p className="flex items-center text-gray-700">
                      <Calendar className="mr-2 text-blue-600" size={16} />
                      <span className="font-medium">Date:</span>
                      <span className="ml-1">{exp.date}</span>
                    </p>
                    <p className="flex items-center text-gray-700">
                      <span className="font-medium">Description:</span>
                      <span className="ml-1">{exp.description}</span>
                    </p>
                    <p className="flex items-center text-gray-700">
                      <DollarSign className="mr-2 text-blue-600" size={16} />
                      <span className="font-medium">Montant:</span>
                      <span className="ml-1">{exp.amount} {exp.currency}</span>
                    </p>
                    {exp.convertedCHF && exp.currency !== "CHF" && (
                    <p className="text-sm text-green-600 mt-1">
                      ≈ {exp.convertedCHF} CHF
                    </p>
                    )}
                    <p className="flex items-center text-gray-700">
                      <Tag className="mr-2 text-blue-600" size={16} />
                      <span className="font-medium">Catégorie:</span>
                      <span className="ml-1">{exp.category}</span>
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Expenses;




import { useState, useEffect } from "react";
import { Calendar, DollarSign, Tag, Receipt, PlusCircle, FileUp, Paperclip, AlertCircle, CheckCircle } from "lucide-react";
import axios from "axios";

function Expenses() {
  const [expenseData, setExpenseData] = useState({
    date: "",
    description: "",
    amount: "",
    currency: "EUR",
    category: "",
    receipt: null,
    receiptName: "",
  });

  const [convertedAmount, setConvertedAmount] = useState(null);
  const [exchangeRate, setExchangeRate] = useState(null);
  const [expenses, setExpenses] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Configuration d'Axios avec le token d'authentification
  const axiosInstance = axios.create({
    baseURL: 'http://localhost:5000',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    }
  });

  useEffect(() => {
    // Charger les dépenses existantes au chargement du composant
    fetchExpenses();
  }, []);

  const fetchExpenses = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError("Vous devez être connecté pour voir vos dépenses");
        return;
      }

      const response = await axiosInstance.get('/api/expenses');
      setExpenses(response.data);
    } catch (err) {
      console.error("Erreur:", err);
      setError(err.response?.data?.message || err.message);
    }
  };

  const handleChange = async (e) => {
    const { name, value } = e.target;
    const newData = { ...expenseData, [name]: value };
    setExpenseData(newData);

    // Recalculer taux/conversion si "currency" ou "amount" change
    if (name === "currency" || name === "amount") {
      if (newData.currency !== "CHF" && newData.amount > 0) {
        try {
          const res = await axios.get(`https://api.frankfurter.app/latest`, {
            params: {
              amount: newData.amount,
              from: newData.currency,
              to: 'CHF'
            }
          });
          setConvertedAmount(res.data.rates.CHF.toFixed(2));
          setExchangeRate(res.data.rates.CHF);
        } catch (error) {
          console.error("Erreur de conversion :", error);
          setConvertedAmount(null);
          setExchangeRate(null);
        }
      } else {
        setConvertedAmount(null);
        setExchangeRate(null);
      }
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setExpenseData({
        ...expenseData,
        receipt: file,
        receiptName: file.name
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);
    
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error("Vous devez être connecté pour ajouter une dépense");
      }
      
      // Créer un objet FormData pour envoyer des fichiers
      const formData = new FormData();
      formData.append('date', expenseData.date);
      formData.append('description', expenseData.description);
      formData.append('amount', expenseData.amount);
      formData.append('currency', expenseData.currency);
      formData.append('category', expenseData.category);
      
      // Ajouter le montant converti si disponible
      if (convertedAmount) {
        formData.append('convertedAmount', convertedAmount);
      }
      
      // Ajouter le fichier justificatif si disponible
      if (expenseData.receipt) {
        formData.append('receipt', expenseData.receipt);
      }
      
      const response = await axiosInstance.post('/api/expenses/add', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      // Ajouter la nouvelle dépense à la liste
      setExpenses([...expenses, response.data]);
      setSuccessMessage("Dépense ajoutée avec succès!");
      
      // Réinitialiser le formulaire
      setExpenseData({
        date: "",
        description: "",
        amount: "",
        currency: "EUR",
        category: "",
        receipt: null,
        receiptName: "",
      });
      setConvertedAmount(null);
      setExchangeRate(null);
      
      // Réinitialiser le champ de fichier
      const fileInput = document.getElementById('receipt');
      if (fileInput) fileInput.value = '';
      
    } catch (err) {
      console.error("Erreur:", err);
      setError(err.response?.data?.message || err.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="md:ml-64 p-4 sm:p-6 bg-gray-100 min-h-screen">
      <div className="max-w-xl mx-auto">
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 sm:p-6 rounded-t-xl shadow-md">
          <h2 className="text-xl sm:text-2xl font-bold mb-1 flex items-center">
            <Receipt className="mr-2" size={24} />
            Déclaration de frais
          </h2>
          <p className="text-blue-100 text-sm">Enregistrez et gérez vos dépenses professionnelles</p>
        </div>

        {/* Messages d'erreur ou de succès */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="text-red-500 mr-2" size={20} />
              <p className="text-red-800">{error}</p>
            </div>
          </div>
        )}
        
        {successMessage && (
          <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-4 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="text-green-500 mr-2" size={20} />
              <p className="text-green-800">{successMessage}</p>
            </div>
          </div>
        )}

        {/* Formulaire */}
        <form onSubmit={handleSubmit} className="bg-white p-4 sm:p-6 rounded-b-xl shadow-md mb-6">
          <div className="space-y-4">
            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <Calendar className="mr-2" size={16} />
                Date
              </label>
              <input
                type="date"
                name="date"
                value={expenseData.date}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
                required
              />
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700">Description</label>
              <textarea
                name="description"
                value={expenseData.description}
                onChange={(e) => {
                  handleChange(e);
                }}
                placeholder="Ex : Déjeuner client, billet train..."
                className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all resize-none overflow-hidden min-h-12"
                rows={3}
                required
              />
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <DollarSign className="mr-2" size={16} />
                Montant
              </label>
              <input
                type="number"
                name="amount"
                value={expenseData.amount}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
                min="0"
                required
              />
              {convertedAmount && (
                <p className="text-sm text-green-600 mt-1">
                  ≈ {convertedAmount} CHF (Taux : {exchangeRate?.toFixed(4)})
                </p>
              )}
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700">Devise</label>
              <select
                name="currency"
                value={expenseData.currency}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
              >
                <option value="EUR">€ EUR</option>
                <option value="USD">$ USD</option>
                <option value="CHF">CHF</option>
              </select>
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <Tag className="mr-2" size={16} />
                Catégorie
              </label>
              <select
                name="category"
                value={expenseData.category}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
                required
              >
                <option value="">-- Sélectionner --</option>
                <option value="Transport">Transport</option>
                <option value="Hébergement">Hébergement</option>
                <option value="Restauration">Restauration</option>
                <option value="Autre">Autre</option>
              </select>
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <FileUp className="mr-2" size={16} />
                Justificatif
              </label>
              <div className="relative">
                <input
                  type="file"
                  id="receipt"
                  name="receipt"
                  onChange={handleFileChange}
                  className="hidden"
                  accept=".pdf,.jpg,.jpeg,.png"
                />
                <div className="flex items-center gap-2">
                  <label
                    htmlFor="receipt"
                    className="cursor-pointer bg-gray-100 border border-gray-300 rounded p-2 hover:bg-gray-200 transition-colors flex items-center"
                  >
                    <Paperclip size={16} className="mr-2" />
                    Choisir un fichier
                  </label>
                  {expenseData.receiptName ? (
                    <span className="text-sm text-gray-700 truncate max-w-xs">
                      {expenseData.receiptName}
                    </span>
                  ) : (
                    <span className="text-sm text-gray-500 italic">Aucun fichier sélectionné</span>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Formats acceptés: PDF, JPG, JPEG, PNG
                </p>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className={`bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors w-full sm:w-auto flex items-center justify-center ${
                isLoading ? "opacity-70 cursor-not-allowed" : ""
              }`}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Traitement...
                </>
              ) : (
                <>
                  <PlusCircle className="mr-2" size={18} />
                  Enregistrer la dépense
                </>
              )}
            </button>
          </div>
        </form>

        {/* Liste des dépenses */}
        {expenses.length > 0 && (
          <div className="bg-white p-4 sm:p-6 rounded-xl shadow-md">
            <h3 className="text-lg sm:text-xl font-semibold mb-4 text-gray-800 border-b pb-2">
              Dépenses enregistrées
            </h3>
            <div className="space-y-3">
              {expenses.map((exp, index) => (
                <div
                  key={index}
                  className="border border-gray-200 p-4 rounded-lg bg-gray-50 hover:bg-blue-50 transition-colors"
                >
                  <div className="grid grid-cols-1 gap-2">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      <p className="flex items-center text-gray-700">
                        <Calendar className="mr-2 text-blue-600" size={16} />
                        <span className="font-medium">Date:</span>
                        <span className="ml-1">{exp.date}</span>
                      </p>
                      <p className="flex items-center text-gray-700">
                        <DollarSign className="mr-2 text-blue-600" size={16} />
                        <span className="font-medium">Montant:</span>
                        <span className="ml-1">{exp.amount} {exp.currency}</span>
                        {exp.convertedAmount && exp.currency !== "CHF" && (
                          <span className="ml-2 text-sm text-green-600">
                            (≈ {exp.convertedAmount} CHF)
                          </span>
                        )}
                      </p>
                      <p className="flex items-center text-gray-700">
                        <Tag className="mr-2 text-blue-600" size={16} />
                        <span className="font-medium">Catégorie:</span>
                        <span className="ml-1">{exp.category}</span>
                      </p>
                    </div>
                    
                    <div className="mt-2 pt-2 border-t border-gray-200">
                      <p className="text-gray-700">
                        <span className="font-medium">Description:</span>
                        <span className="ml-1 whitespace-pre-wrap">{exp.description}</span>
                      </p>
                    </div>
                    
                    {exp.receiptUrl && (
                      <div className="mt-2 pt-2 border-t border-gray-200">
                        <p className="flex items-center text-gray-700">
                          <Paperclip className="mr-2 text-blue-600" size={16} />
                          <span className="font-medium">Justificatif:</span>
                          <a 
                            href={`http://localhost:5000${exp.receiptUrl}`}
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="ml-1 text-blue-600 hover:underline flex items-center"
                          >
                            Voir le justificatif
                            <FileUp size={12} className="ml-1" />
                          </a>
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Expenses;


//version fontionelle sa api


import { useState, useEffect } from "react";
import { Calendar, DollarSign, Tag, Receipt, PlusCircle, FileUp, Paperclip } from "lucide-react";

function Expenses() {
  const [expenseData, setExpenseData] = useState({
    date: "",
    description: "",
    amount: "",
    currency: "EUR",
    category: "",
    receipt: null,
    receiptName: "",
  });

  const [convertedAmount, setConvertedAmount] = useState(null);
  const [exchangeRate, setExchangeRate] = useState(null);
  const [expenses, setExpenses] = useState([]);

  const handleChange = async (e) => {
    const { name, value } = e.target;
    const newData = { ...expenseData, [name]: value };
    setExpenseData(newData);

    // Recalculer taux/conversion si "currency" ou "amount" change
    if (name === "currency" || name === "amount") {
      if (newData.currency !== "CHF" && newData.amount > 0) {
        try {
          const res = await fetch(`https://api.frankfurter.app/latest?amount=${newData.amount}&from=${newData.currency}&to=CHF`);
          const data = await res.json();
          setConvertedAmount(data.rates.CHF.toFixed(2));
          setExchangeRate(data.rates.CHF);
        } catch (error) {
          console.error("Erreur de conversion :", error);
          setConvertedAmount(null);
          setExchangeRate(null);
        }
      } else {
        setConvertedAmount(null);
        setExchangeRate(null);
      }
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setExpenseData({
        ...expenseData,
        receipt: file,
        receiptName: file.name
      });
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Pour simuler l'URL d'un fichier téléchargé (en production, cela viendrait d'un backend)
    const receiptURL = expenseData.receipt ? URL.createObjectURL(expenseData.receipt) : null;
    
    setExpenses([
      ...expenses,
      {
        ...expenseData,
        convertedCHF: convertedAmount,
        receiptURL: receiptURL,
      },
    ]);
    
    setExpenseData({
      date: "",
      description: "",
      amount: "",
      currency: "EUR",
      category: "",
      receipt: null,
      receiptName: "",
    });
    setConvertedAmount(null);
    setExchangeRate(null);
    
    // Réinitialiser le champ de fichier
    const fileInput = document.getElementById('receipt');
    if (fileInput) fileInput.value = '';
  };

  return (
    <div className="md:ml-64 p-4 sm:p-6 bg-gray-100 min-h-screen">
      <div className="max-w-xl mx-auto">
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 sm:p-6 rounded-t-xl shadow-md">
          <h2 className="text-xl sm:text-2xl font-bold mb-1 flex items-center">
            <Receipt className="mr-2" size={24} />
            Déclaration de frais
          </h2>
          <p className="text-blue-100 text-sm">Enregistrez et gérez vos dépenses professionnelles</p>
        </div>

        {/* Formulaire */}
        <form onSubmit={handleSubmit} className="bg-white p-4 sm:p-6 rounded-b-xl shadow-md mb-6">
          <div className="space-y-4">
            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <Calendar className="mr-2" size={16} />
                Date
              </label>
              <input
                type="date"
                name="date"
                value={expenseData.date}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
                required
              />
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700">Description</label>
              <textarea
                name="description"
                value={expenseData.description}
                onChange={(e) => {
                  handleChange(e);
                }}
                placeholder="Ex : Déjeuner client, billet train..."
                className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all resize-none overflow-hidden min-h-12"
                rows={3}
                required
              />
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <DollarSign className="mr-2" size={16} />
                Montant
              </label>
              <input
                type="number"
                name="amount"
                value={expenseData.amount}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
                min="0"
                required
              />
              {convertedAmount && (
                <p className="text-sm text-green-600 mt-1">
                  ≈ {convertedAmount} CHF (Taux : {exchangeRate?.toFixed(4)})
                </p>
              )}
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700">Devise</label>
              <select
                name="currency"
                value={expenseData.currency}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
              >
                <option value="EUR">€ EUR</option>
                <option value="USD">$ USD</option>
                <option value="CHF">CHF</option>
              </select>
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <Tag className="mr-2" size={16} />
                Catégorie
              </label>
              <select
                name="category"
                value={expenseData.category}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
                required
              >
                <option value="">-- Sélectionner --</option>
                <option value="Transport">Transport</option>
                <option value="Hébergement">Hébergement</option>
                <option value="Restauration">Restauration</option>
                <option value="Autre">Autre</option>
              </select>
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <FileUp className="mr-2" size={16} />
                Justificatif
              </label>
              <div className="relative">
                <input
                  type="file"
                  id="receipt"
                  name="receipt"
                  onChange={handleFileChange}
                  className="hidden"
                  accept=".pdf,.jpg,.jpeg,.png"
                />
                <div className="flex items-center gap-2">
                  <label
                    htmlFor="receipt"
                    className="cursor-pointer bg-gray-100 border border-gray-300 rounded p-2 hover:bg-gray-200 transition-colors flex items-center"
                  >
                    <Paperclip size={16} className="mr-2" />
                    Choisir un fichier
                  </label>
                  {expenseData.receiptName ? (
                    <span className="text-sm text-gray-700 truncate max-w-xs">
                      {expenseData.receiptName}
                    </span>
                  ) : (
                    <span className="text-sm text-gray-500 italic">Aucun fichier sélectionné</span>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Formats acceptés: PDF, JPG, JPEG, PNG
                </p>
              </div>
            </div>

            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors w-full sm:w-auto flex items-center justify-center"
            >
              <PlusCircle className="mr-2" size={18} />
              Enregistrer la dépense
            </button>
          </div>
        </form>

        {/* Liste des dépenses */}
        {expenses.length > 0 && (
          <div className="bg-white p-4 sm:p-6 rounded-xl shadow-md">
            <h3 className="text-lg sm:text-xl font-semibold mb-4 text-gray-800 border-b pb-2">
              Dépenses enregistrées
            </h3>
            <div className="space-y-3">
              {expenses.map((exp, index) => (
                <div
                  key={index}
                  className="border border-gray-200 p-4 rounded-lg bg-gray-50 hover:bg-blue-50 transition-colors"
                >
                  <div className="grid grid-cols-1 gap-2">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      <p className="flex items-center text-gray-700">
                        <Calendar className="mr-2 text-blue-600" size={16} />
                        <span className="font-medium">Date:</span>
                        <span className="ml-1">{exp.date}</span>
                      </p>
                      <p className="flex items-center text-gray-700">
                        <DollarSign className="mr-2 text-blue-600" size={16} />
                        <span className="font-medium">Montant:</span>
                        <span className="ml-1">{exp.amount} {exp.currency}</span>
                        {exp.convertedCHF && exp.currency !== "CHF" && (
                          <span className="ml-2 text-sm text-green-600">
                            (≈ {exp.convertedCHF} CHF)
                          </span>
                        )}
                      </p>
                      <p className="flex items-center text-gray-700">
                        <Tag className="mr-2 text-blue-600" size={16} />
                        <span className="font-medium">Catégorie:</span>
                        <span className="ml-1">{exp.category}</span>
                      </p>
                    </div>
                    
                    <div className="mt-2 pt-2 border-t border-gray-200">
                      <p className="text-gray-700">
                        <span className="font-medium">Description:</span>
                        <span className="ml-1 whitespace-pre-wrap">{exp.description}</span>
                      </p>
                    </div>
                    
                    {exp.receiptName && (
                      <div className="mt-2 pt-2 border-t border-gray-200">
                        <p className="flex items-center text-gray-700">
                          <Paperclip className="mr-2 text-blue-600" size={16} />
                          <span className="font-medium">Justificatif:</span>
                          <a 
                            href={exp.receiptURL} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="ml-1 text-blue-600 hover:underline flex items-center"
                          >
                            {exp.receiptName}
                            <FileUp size={12} className="ml-1" />
                          </a>
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Expenses;
