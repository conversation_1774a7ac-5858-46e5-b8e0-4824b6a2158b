import { Table } from '@tanstack/react-table'
import { IconX } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { DataTableViewOptions } from '@/features/timesheet/components/data-table-view-options'
import { DataTableFacetedFilter } from '@/features/timesheet/components/data-table-faceted-filter'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
}

export function ExpenseItemsDataTableToolbar<TData>({
  table,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  const statusOptions = [
    {
      label: '📝 Draft',
      value: 'draft',
    },
    {
      label: '📤 Submitted',
      value: 'submitted',
    },
    {
      label: '✅ Approved',
      value: 'approved',
    },
    {
      label: '❌ Rejected',
      value: 'rejected',
    },
  ]

  const clientOptions = [
    {
      label: 'Acme Corporation',
      value: 'Acme Corporation',
    },
    {
      label: 'TechStart Inc.',
      value: 'TechStart Inc.',
    },
    {
      label: 'Global Solutions Ltd.',
      value: 'Global Solutions Ltd.',
    },
    {
      label: 'Innovation Labs',
      value: 'Innovation Labs',
    },
    {
      label: 'Digital Dynamics',
      value: 'Digital Dynamics',
    },
  ]

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center space-x-2'>
        <Input
          placeholder='Filter by title...'
          value={(table.getColumn('title')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('title')?.setFilterValue(event.target.value)
          }
          className='h-8 w-[150px] lg:w-[250px]'
        />
        {table.getColumn('status') && (
          <DataTableFacetedFilter
            column={table.getColumn('status')}
            title='Status'
            options={statusOptions}
          />
        )}
        {table.getColumn('client') && (
          <DataTableFacetedFilter
            column={table.getColumn('client')}
            title='Client'
            options={clientOptions}
          />
        )}
        {isFiltered && (
          <Button
            variant='ghost'
            onClick={() => table.resetColumnFilters()}
            className='h-8 px-2 lg:px-3'
          >
            Reset
            <IconX className='ml-2 h-4 w-4' />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}
