import { NonWorkingDay } from './schema'

export const mockNonWorkingDays: NonWorkingDay[] = [
  {
    id: '1',
    startDate: '2024-12-25',
    endDate: '2024-12-25',
    reason: 'public_holiday',
    consultantId: 'consultant-1',
    createdAt: '2024-12-01T10:00:00Z',
    updatedAt: '2024-12-01T10:00:00Z',
  },
  {
    id: '2',
    startDate: '2024-12-24',
    endDate: '2024-12-24',
    reason: 'public_holiday',
    consultantId: 'consultant-1',
    createdAt: '2024-12-01T10:00:00Z',
    updatedAt: '2024-12-01T10:00:00Z',
  },
  {
    id: '3',
    startDate: '2024-11-15',
    endDate: '2024-11-17',
    reason: 'sick_leave',
    consultantId: 'consultant-1',
    createdAt: '2024-11-15T08:00:00Z',
    updatedAt: '2024-11-15T08:00:00Z',
  },
  {
    id: '4',
    startDate: '2024-10-21',
    endDate: '2024-10-25',
    reason: 'paid_leave',
    consultantId: 'consultant-1',
    createdAt: '2024-10-01T09:00:00Z',
    updatedAt: '2024-10-01T09:00:00Z',
  },
  {
    id: '5',
    startDate: '2024-09-10',
    endDate: '2024-09-10',
    reason: 'other',
    customReason: 'Medical appointment',
    consultantId: 'consultant-1',
    createdAt: '2024-09-05T14:00:00Z',
    updatedAt: '2024-09-05T14:00:00Z',
  },
  {
    id: '6',
    startDate: '2024-08-12',
    endDate: '2024-08-16',
    reason: 'paid_leave',
    consultantId: 'consultant-1',
    createdAt: '2024-07-20T11:00:00Z',
    updatedAt: '2024-07-20T11:00:00Z',
  },
  {
    id: '7',
    startDate: '2024-07-04',
    endDate: '2024-07-04',
    reason: 'public_holiday',
    consultantId: 'consultant-1',
    createdAt: '2024-06-15T10:00:00Z',
    updatedAt: '2024-06-15T10:00:00Z',
  },
  {
    id: '8',
    startDate: '2024-06-03',
    endDate: '2024-06-05',
    reason: 'unpaid_leave',
    consultantId: 'consultant-1',
    createdAt: '2024-05-25T16:00:00Z',
    updatedAt: '2024-05-25T16:00:00Z',
  },
]
