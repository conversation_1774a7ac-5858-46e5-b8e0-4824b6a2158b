import { z } from 'zod'

export const nonWorkingDaySchema = z.object({
  id: z.string(),
  startDate: z.string(),
  endDate: z.string(),
  reason: z.enum(['public_holiday', 'sick_leave', 'paid_leave', 'unpaid_leave', 'other']),
  customReason: z.string().optional(),
  consultantId: z.string(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
})

export const nonWorkingDayFormSchema = z.object({
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  reason: z.enum(['public_holiday', 'sick_leave', 'paid_leave', 'unpaid_leave', 'other'], {
    required_error: 'Please select a reason',
  }),
  customReason: z.string().optional(),
}).refine((data) => {
  const startDate = new Date(data.startDate)
  const endDate = new Date(data.endDate)
  return endDate >= startDate
}, {
  message: 'End date must be after or equal to start date',
  path: ['endDate'],
}).refine((data) => {
  if (data.reason === 'other') {
    return data.customReason && data.customReason.trim().length > 0
  }
  return true
}, {
  message: 'Custom reason is required when "Other" is selected',
  path: ['customReason'],
})

export type NonWorkingDay = z.infer<typeof nonWorkingDaySchema>
export type NonWorkingDayForm = z.infer<typeof nonWorkingDayFormSchema>

export const reasonOptions = [
  { value: 'public_holiday', label: '🏛️ Public Holiday' },
  { value: 'sick_leave', label: '🤒 Sick Leave' },
  { value: 'paid_leave', label: '🏖️ Paid Leave' },
  { value: 'unpaid_leave', label: '📅 Unpaid Leave' },
  { value: 'other', label: '📝 Other' },
] as const
