import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { DataTableColumnHeader } from '@/features/tasks/components/data-table-column-header'
import { IconDotsHorizontal, IconEdit, IconTrash } from '@tabler/icons-react'
import { NonWorkingDay, reasonOptions } from '../data/schema'
import { useNonWorkingDays } from '../context/non-working-days-context'

const reasonColors = {
  public_holiday: 'default',
  sick_leave: 'destructive',
  paid_leave: 'default',
  unpaid_leave: 'secondary',
  other: 'outline',
} as const

function ActionsCell({ row }: { row: { original: NonWorkingDay } }) {
  const { setOpen, setCurrentItem } = useNonWorkingDays()
  const item = row.original

  const handleEdit = () => {
    setCurrentItem(item)
    setOpen('update')
  }

  const handleDelete = () => {
    setCurrentItem(item)
    setOpen('delete')
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' className='h-8 w-8 p-0'>
          <span className='sr-only'>Open menu</span>
          <IconDotsHorizontal className='h-4 w-4' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleEdit}>
          <IconEdit className='mr-2 h-4 w-4' />
          Edit
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleDelete} className='text-destructive'>
          <IconTrash className='mr-2 h-4 w-4' />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export const nonWorkingDaysColumns: ColumnDef<NonWorkingDay>[] = [
  {
    accessorKey: 'startDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Start Date' />
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue('startDate'))
      return (
        <div className='font-medium'>
          {date.toLocaleDateString('en-US', {
            weekday: 'short',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
          })}
        </div>
      )
    },
  },
  {
    accessorKey: 'endDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='End Date' />
    ),
    cell: ({ row }) => {
      const startDate = row.getValue('startDate') as string
      const endDate = row.getValue('endDate') as string

      if (startDate === endDate) {
        return <div className='text-muted-foreground'>Same day</div>
      }

      const date = new Date(endDate)
      return (
        <div className='font-medium'>
          {date.toLocaleDateString('en-US', {
            weekday: 'short',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
          })}
        </div>
      )
    },
  },
  {
    accessorKey: 'reason',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Reason' />
    ),
    cell: ({ row }) => {
      const reason = row.getValue('reason') as NonWorkingDay['reason']
      const customReason = row.original.customReason

      const reasonOption = reasonOptions.find(option => option.value === reason)
      const label = reasonOption?.label || reason

      return (
        <div className='space-y-1'>
          <Badge variant={reasonColors[reason]}>
            {label}
          </Badge>
          {reason === 'other' && customReason && (
            <div className='text-xs text-muted-foreground'>
              {customReason}
            </div>
          )}
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'duration',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Duration' />
    ),
    cell: ({ row }) => {
      const startDate = new Date(row.getValue('startDate') as string)
      const endDate = new Date(row.getValue('endDate') as string)

      const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1

      return (
        <div className='font-medium'>
          {diffDays} {diffDays === 1 ? 'day' : 'days'}
        </div>
      )
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Created' />
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue('createdAt') as string)
      return (
        <div className='text-sm text-muted-foreground'>
          {date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          })}
        </div>
      )
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ActionsCell,
  },
]
