import { toast } from '@/hooks/use-toast'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { useNonWorkingDays } from '../context/non-working-days-context'
import { NonWorkingDayMutateDrawer } from './non-working-day-mutate-drawer'

export function NonWorkingDaysDialogs() {
  const { 
    open, 
    setOpen, 
    currentItem, 
    setCurrentItem,
  } = useNonWorkingDays()

  const handleDelete = () => {
    if (!currentItem) return
    
    // TODO: Implement actual delete API call
    console.log('Deleting non-working day:', currentItem.id)
    
    toast({
      title: 'Non-working day deleted',
      description: 'Successfully deleted the non-working day.',
    })
    
    setCurrentItem(null)
    setOpen(null)
  }

  return (
    <>
      {/* Create Non-working Day */}
      <NonWorkingDayMutateDrawer
        key='non-working-day-create'
        open={open === 'create'}
        onOpenChange={() => setOpen('create')}
      />

      {/* Update Non-working Day */}
      {currentItem && (
        <NonWorkingDayMutateDrawer
          key={`non-working-day-update-${currentItem.id}`}
          open={open === 'update'}
          onOpenChange={() => {
            setOpen('update')
            setTimeout(() => {
              setCurrentItem(null)
            }, 500)
          }}
          currentItem={currentItem}
        />
      )}

      {/* Delete Non-working Day Confirmation */}
      {currentItem && (
        <ConfirmDialog
          open={open === 'delete'}
          onOpenChange={() => setOpen('delete')}
          handleConfirm={handleDelete}
          title='Delete Non-working Day'
          desc={`Are you sure you want to delete this non-working day? This action cannot be undone.`}
        />
      )}
    </>
  )
}
