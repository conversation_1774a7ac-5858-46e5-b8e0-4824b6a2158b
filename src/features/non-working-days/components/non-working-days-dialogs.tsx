import { toast } from '@/hooks/use-toast'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { useNonWorkingDays } from '../context/non-working-days-context'
import { NonWorkingDayMutateDrawer } from './non-working-day-mutate-drawer'

export function NonWorkingDaysDialogs() {
  const {
    open,
    setOpen,
    currentItem,
    setCurrentItem,
  } = useNonWorkingDays()

  const handleDelete = () => {
    if (!currentItem) return

    // TODO: Implement actual delete API call
    console.log('Deleting non-working day:', currentItem.id)

    toast({
      title: 'Non-working day deleted',
      description: 'Successfully deleted the non-working day.',
    })

    setCurrentItem(null)
    setOpen(null)
  }

  return (
    <>
      {/* Create Non-working Day */}
      <NonWorkingDayMutateDrawer
        key='non-working-day-create'
        open={open === 'create'}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            setOpen(null)
          }
        }}
      />

      {/* Update Non-working Day */}
      {currentItem && (
        <NonWorkingDayMutateDrawer
          key={`non-working-day-update-${currentItem.id}`}
          open={open === 'update'}
          onOpenChange={(isOpen) => {
            if (!isOpen) {
              setOpen(null)
              setTimeout(() => {
                setCurrentItem(null)
              }, 300)
            }
          }}
          currentItem={currentItem}
        />
      )}

      {/* Delete Non-working Day Confirmation */}
      {currentItem && (
        <ConfirmDialog
          open={open === 'delete'}
          onOpenChange={(isOpen) => {
            if (!isOpen) {
              setOpen(null)
            }
          }}
          handleConfirm={handleDelete}
          title='Delete Non-working Day'
          desc={`Are you sure you want to delete this non-working day? This action cannot be undone.`}
        />
      )}
    </>
  )
}
