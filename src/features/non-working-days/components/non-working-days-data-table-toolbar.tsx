import { Table } from '@tanstack/react-table'
import { IconX } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { DataTableViewOptions } from '@/features/tasks/components/data-table-view-options'
import { DataTableFacetedFilter } from '@/features/tasks/components/data-table-faceted-filter'
import { reasonOptions } from '../data/schema'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
}

export function NonWorkingDaysDataTableToolbar<TData>({
  table,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center space-x-2'>
        <Input
          placeholder='Filter by custom reason...'
          value={(table.getColumn('reason')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('reason')?.setFilterValue(event.target.value)
          }
          className='h-8 w-[150px] lg:w-[250px]'
        />
        {table.getColumn('reason') && (
          <DataTableFacetedFilter
            column={table.getColumn('reason')}
            title='Reason'
            options={reasonOptions.map(option => ({
              label: option.label,
              value: option.value,
            }))}
          />
        )}
        {isFiltered && (
          <Button
            variant='ghost'
            onClick={() => table.resetColumnFilters()}
            className='h-8 px-2 lg:px-3'
          >
            Reset
            <IconX className='ml-2 h-4 w-4' />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}
