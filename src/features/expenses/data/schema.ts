import { z } from 'zod'

export const expenseItemSchema = z.object({
  id: z.string(),
  title: z.string(),
  startDate: z.string(),
  endDate: z.string(),
  client: z.string(),
  description: z.string().optional(),
  totalAmount: z.number(),
  expenseCount: z.number(),
  status: z.enum(['draft', 'submitted', 'approved', 'rejected']),
  consultantId: z.string(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
})

export const expenseSchema = z.object({
  id: z.string(),
  itemId: z.string(),
  designation: z.string(),
  amount: z.number(),
  currency: z.string(),
  amountCHF: z.number().optional(),
  exchangeRate: z.number().optional(),
  date: z.string(),
  category: z.enum(['travel', 'accommodation', 'meals', 'transportation', 'office_supplies', 'software', 'training', 'marketing', 'communication', 'other']),
  receiptUrl: z.string().optional(),
  comment: z.string().optional(),
  consultantId: z.string(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
})

export const expenseItemFormSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  client: z.string().min(1, 'Please select a client'),
  description: z.string().optional(),
}).refine((data) => {
  const startDate = new Date(data.startDate)
  const endDate = new Date(data.endDate)
  return endDate >= startDate
}, {
  message: 'End date must be after or equal to start date',
  path: ['endDate'],
})

export const expenseFormSchema = z.object({
  designation: z.string().min(1, 'Designation is required'),
  amount: z.string()
    .min(1, 'Amount is required')
    .refine((val) => {
      const num = parseFloat(val)
      return !isNaN(num) && num > 0
    }, 'Amount must be a positive number'),
  currency: z.string().min(1, 'Please select a currency'),
  date: z.string().min(1, 'Date is required'),
  category: z.enum(['travel', 'accommodation', 'meals', 'transportation', 'office_supplies', 'software', 'training', 'marketing', 'communication', 'other'], {
    required_error: 'Please select a category',
  }),
  comment: z.string().optional(),
})

export const clientSchema = z.object({
  id: z.string(),
  name: z.string(),
  code: z.string().optional(),
})

export type ExpenseItem = z.infer<typeof expenseItemSchema>
export type Expense = z.infer<typeof expenseSchema>
export type ExpenseItemForm = z.infer<typeof expenseItemFormSchema>
export type ExpenseForm = z.infer<typeof expenseFormSchema>
export type Client = z.infer<typeof clientSchema>
