import { ExpenseItem, Expense, Client } from './schema'

export const mockClients: Client[] = [
  {
    id: '1',
    name: 'Acme Corporation',
    code: 'ACME',
  },
  {
    id: '2',
    name: 'TechStart Inc.',
    code: 'TECH',
  },
  {
    id: '3',
    name: 'Global Solutions Ltd.',
    code: 'GLOB',
  },
  {
    id: '4',
    name: 'Innovation Labs',
    code: 'INNO',
  },
  {
    id: '5',
    name: 'Digital Dynamics',
    code: 'DIGI',
  },
]

export const currencies = [
  { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
]

export const expenseCategories = [
  { value: 'travel', label: '✈️ Travel & Transportation' },
  { value: 'accommodation', label: '🏨 Accommodation' },
  { value: 'meals', label: '🍽️ Meals & Entertainment' },
  { value: 'transportation', label: '🚗 Local Transportation' },
  { value: 'office_supplies', label: '📎 Office Supplies' },
  { value: 'software', label: '💻 Software & Tools' },
  { value: 'training', label: '📚 Training & Education' },
  { value: 'marketing', label: '📢 Marketing & Advertising' },
  { value: 'communication', label: '📞 Communication' },
  { value: 'other', label: '📋 Other' },
]

export const mockExpenseItems: ExpenseItem[] = [
  {
    id: '1',
    title: 'Client Meeting - Zurich',
    startDate: '2024-01-15',
    endDate: '2024-01-17',
    client: 'Acme Corporation',
    description: 'Business trip to Zurich for quarterly review meeting',
    totalAmount: 1250.50,
    expenseCount: 5,
    status: 'approved',
    consultantId: 'consultant-1',
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-18T14:30:00Z',
  },
  {
    id: '2',
    title: 'Project Kickoff - Berlin',
    startDate: '2024-01-22',
    endDate: '2024-01-24',
    client: 'TechStart Inc.',
    description: 'Initial project setup and team alignment meetings',
    totalAmount: 890.75,
    expenseCount: 4,
    status: 'submitted',
    consultantId: 'consultant-1',
    createdAt: '2024-01-22T08:15:00Z',
    updatedAt: '2024-01-25T10:45:00Z',
  },
  {
    id: '3',
    title: 'Training Workshop - London',
    startDate: '2024-02-05',
    endDate: '2024-02-07',
    client: 'Global Solutions Ltd.',
    description: 'Advanced technical training for development team',
    totalAmount: 2150.25,
    expenseCount: 7,
    status: 'draft',
    consultantId: 'consultant-1',
    createdAt: '2024-02-05T07:30:00Z',
    updatedAt: '2024-02-07T16:20:00Z',
  },
  {
    id: '4',
    title: 'Conference - San Francisco',
    startDate: '2024-02-12',
    endDate: '2024-02-15',
    client: 'Innovation Labs',
    description: 'Tech conference attendance and networking',
    totalAmount: 3200.00,
    expenseCount: 8,
    status: 'approved',
    consultantId: 'consultant-1',
    createdAt: '2024-02-12T06:00:00Z',
    updatedAt: '2024-02-16T12:00:00Z',
  },
  {
    id: '5',
    title: 'Client Workshop - Paris',
    startDate: '2024-02-20',
    endDate: '2024-02-21',
    client: 'Digital Dynamics',
    description: 'Product demonstration and user training',
    totalAmount: 650.80,
    expenseCount: 3,
    status: 'rejected',
    consultantId: 'consultant-1',
    createdAt: '2024-02-20T09:45:00Z',
    updatedAt: '2024-02-22T11:15:00Z',
  },
]

export const mockExpenses: Expense[] = [
  // Expenses for "Client Meeting - Zurich" (item 1)
  {
    id: '1',
    itemId: '1',
    designation: 'Flight tickets Zurich',
    amount: 320.00,
    currency: 'EUR',
    amountCHF: 345.60,
    exchangeRate: 1.08,
    date: '2024-01-15',
    category: 'travel',
    receiptUrl: '/receipts/flight-zurich-001.pdf',
    comment: 'Round trip flight tickets',
    consultantId: 'consultant-1',
    createdAt: '2024-01-15T09:00:00Z',
  },
  {
    id: '2',
    itemId: '1',
    designation: 'Hotel accommodation',
    amount: 450.00,
    currency: 'CHF',
    date: '2024-01-15',
    category: 'accommodation',
    receiptUrl: '/receipts/hotel-zurich-001.pdf',
    comment: '2 nights at business hotel',
    consultantId: 'consultant-1',
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '3',
    itemId: '1',
    designation: 'Client dinner',
    amount: 180.50,
    currency: 'CHF',
    date: '2024-01-16',
    category: 'meals',
    receiptUrl: '/receipts/dinner-zurich-001.pdf',
    comment: 'Business dinner with client team',
    consultantId: 'consultant-1',
    createdAt: '2024-01-16T20:15:00Z',
  },
  // Expenses for "Project Kickoff - Berlin" (item 2)
  {
    id: '4',
    itemId: '2',
    designation: 'Train tickets to Berlin',
    amount: 150.00,
    currency: 'EUR',
    amountCHF: 162.00,
    exchangeRate: 1.08,
    date: '2024-01-22',
    category: 'travel',
    receiptUrl: '/receipts/train-berlin-001.pdf',
    consultantId: 'consultant-1',
    createdAt: '2024-01-22T08:15:00Z',
  },
  {
    id: '5',
    itemId: '2',
    designation: 'Hotel Berlin',
    amount: 280.00,
    currency: 'EUR',
    amountCHF: 302.40,
    exchangeRate: 1.08,
    date: '2024-01-22',
    category: 'accommodation',
    receiptUrl: '/receipts/hotel-berlin-001.pdf',
    comment: '2 nights accommodation',
    consultantId: 'consultant-1',
    createdAt: '2024-01-22T15:30:00Z',
  },
]
