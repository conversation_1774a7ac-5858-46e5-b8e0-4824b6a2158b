export interface ExpenseItem {
  id: string
  title: string
  startDate: string
  endDate: string
  client: string
  description?: string
  totalAmount: number // Total in CHF
  expenseCount: number
  status: 'draft' | 'submitted' | 'approved' | 'rejected'
  consultantId: string
  createdAt?: string
  updatedAt?: string
}

export interface Expense {
  id: string
  itemId: string
  designation: string
  amount: number // Amount in original currency
  currency: string
  amountCHF?: number // Converted amount in CHF
  exchangeRate?: number
  date: string
  category: ExpenseCategory
  receipt?: File | string // File object or URL
  receiptUrl?: string
  comment?: string
  consultantId: string
  createdAt?: string
  updatedAt?: string
}

export type ExpenseCategory = 
  | 'travel'
  | 'accommodation'
  | 'meals'
  | 'transportation'
  | 'office_supplies'
  | 'software'
  | 'training'
  | 'marketing'
  | 'communication'
  | 'other'

export interface ExpenseItemFormData {
  title: string
  startDate: string
  endDate: string
  client: string
  description?: string
}

export interface ExpenseFormData {
  designation: string
  amount: string
  currency: string
  date: string
  category: ExpenseCategory
  receipt?: File
  comment?: string
}

export interface Client {
  id: string
  name: string
  code?: string
}

export interface Currency {
  code: string
  name: string
  symbol: string
}

export interface ExchangeRateResponse {
  amount: number
  base: string
  date: string
  rates: Record<string, number>
}

export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}
