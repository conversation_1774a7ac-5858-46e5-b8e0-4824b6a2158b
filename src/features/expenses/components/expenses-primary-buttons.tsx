import { IconDownload, IconPlus } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { useExpenses } from '../context/expenses-context'

export function ExpensesPrimaryButtons() {
  const { setOpen } = useExpenses()
  
  return (
    <div className='flex gap-2'>
      <Button
        variant='outline'
        className='space-x-1'
        onClick={() => {
          // TODO: Implement export functionality
          console.log('Export expenses')
        }}
      >
        <span>Export</span> <IconDownload size={18} />
      </Button>
      <Button className='space-x-1' onClick={() => setOpen('create-item')}>
        <span>New Expense Item</span> <IconPlus size={18} />
      </Button>
    </div>
  )
}
