import { Calendar, Clock, CheckCircle, PlusCircle, FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import type { TimeEntryFormData } from '../types'

interface TimeEntryFormProps {
  formData: TimeEntryFormData
  onFormDataChange: (data: Partial<TimeEntryFormData>) => void
  onSubmit: () => void
  onSetToday: () => void
  loading?: boolean
}

export function TimeEntryForm({
  formData,
  onFormDataChange,
  onSubmit,
  onSetToday,
  loading = false,
}: TimeEntryFormProps) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit()
  }

  const handleChange = (field: keyof TimeEntryFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked

    onFormDataChange({
      [field]: type === 'checkbox' ? checked : value,
    })
  }

  const handleCheckboxChange = (checked: boolean) => {
    onFormDataChange({ remoteWork: checked })
  }

  const adjustTextareaHeight = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    e.target.style.height = 'auto'
    e.target.style.height = e.target.scrollHeight + 'px'
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
          <PlusCircle className="mr-2 h-5 w-5 text-blue-600" />
          Ajouter une entrée
        </CardTitle>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Date field */}
          <div className="flex gap-2">
            <div className="flex-grow">
              <Label htmlFor="date" className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <Calendar className="mr-2 h-4 w-4" />
                Date
              </Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={handleChange('date')}
                className="w-full"
                required
              />
            </div>
            <div className="flex items-end">
              <Button
                type="button"
                variant="outline"
                onClick={onSetToday}
                className="h-10 px-3"
              >
                Aujourd'hui
              </Button>
            </div>
          </div>

          {/* Activity field */}
          <div>
            <Label htmlFor="activity" className="flex items-center text-sm font-medium text-gray-700 mb-1">
              <FileText className="mr-2 h-4 w-4" />
              Activité
            </Label>
            <Input
              id="activity"
              type="text"
              value={formData.activity}
              onChange={handleChange('activity')}
              placeholder="Décrivez votre activité..."
              className="w-full"
              required
            />
          </div>

          {/* Comment field */}
          <div>
            <Label htmlFor="comment" className="flex items-center text-sm font-medium text-gray-700 mb-1">
              <FileText className="mr-2 h-4 w-4" />
              Commentaire (optionnel)
            </Label>
            <Textarea
              id="comment"
              value={formData.comment}
              onChange={(e) => {
                handleChange('comment')(e)
                adjustTextareaHeight(e)
              }}
              placeholder="Ajoutez des détails supplémentaires..."
              className="w-full min-h-[80px] resize-none"
              rows={3}
            />
          </div>

          {/* Hours field */}
          <div>
            <Label htmlFor="hours" className="flex items-center text-sm font-medium text-gray-700 mb-1">
              <Clock className="mr-2 h-4 w-4" />
              Heures travaillées
            </Label>
            <Input
              id="hours"
              type="number"
              value={formData.hours}
              onChange={handleChange('hours')}
              placeholder="Ex: 8 ou 7.5"
              className="w-full"
              min="0"
              step="0.5"
              required
            />
          </div>

          {/* Remote work checkbox */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="remoteWork"
              checked={formData.remoteWork}
              onCheckedChange={handleCheckboxChange}
            />
            <Label
              htmlFor="remoteWork"
              className="flex items-center text-sm font-medium text-gray-700 cursor-pointer"
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              Télétravail
            </Label>
          </div>

          {/* Submit button */}
          <Button
            type="submit"
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            disabled={loading}
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Ajouter à la liste
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
