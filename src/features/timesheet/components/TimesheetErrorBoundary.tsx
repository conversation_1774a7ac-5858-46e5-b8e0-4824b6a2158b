import React from 'react'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface TimesheetErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface TimesheetErrorBoundaryProps {
  children: React.ReactNode
}

export class TimesheetErrorBoundary extends React.Component<
  TimesheetErrorBoundaryProps,
  TimesheetErrorBoundaryState
> {
  constructor(props: TimesheetErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): TimesheetErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Timesheet Error:', error, errorInfo)
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined })
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
          <div className="max-w-2xl mx-auto">
            <Card className="border-red-200">
              <CardHeader className="text-center">
                <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                  <AlertTriangle className="w-6 h-6 text-red-600" />
                </div>
                <CardTitle className="text-red-800">
                  Erreur dans le module Timesheet
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <p className="text-gray-600">
                  Une erreur s'est produite lors du chargement du module de gestion des temps.
                </p>
                
                {this.state.error && (
                  <details className="text-left bg-gray-100 p-4 rounded-lg">
                    <summary className="cursor-pointer font-medium text-gray-800 mb-2">
                      Détails de l'erreur
                    </summary>
                    <pre className="text-sm text-red-600 whitespace-pre-wrap">
                      {this.state.error.message}
                    </pre>
                  </details>
                )}

                <div className="flex gap-4 justify-center">
                  <Button
                    onClick={this.handleRetry}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Réessayer
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => window.location.reload()}
                  >
                    Recharger la page
                  </Button>
                </div>

                <div className="text-sm text-gray-500 mt-6">
                  <p>Si le problème persiste, veuillez contacter le support technique.</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}
