import { Save, Trash2, Edit, Clock, MapPin, Home } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import type { PendingTimeEntry } from '../types'

interface PendingEntriesProps {
  entries: PendingTimeEntry[]
  onRemoveEntry: (tempId: number) => void
  onEditEntry: (entry: PendingTimeEntry) => void
  onSubmitAll: () => void
  loading?: boolean
}

export function PendingEntries({
  entries,
  onRemoveEntry,
  onEditEntry,
  onSubmitAll,
  loading = false,
}: PendingEntriesProps) {
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }
    return new Date(dateString).toLocaleDateString('fr-FR', options)
  }

  const getTotalHours = () => {
    return entries.reduce((total, entry) => total + entry.hours, 0)
  }

  if (entries.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold text-gray-800">
            Entrées en attente
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-gray-500">
            <Clock className="mx-auto h-12 w-12 text-gray-300 mb-3" />
            <p>Aucune entrée en attente</p>
            <p className="text-sm">Ajoutez des entrées avec le formulaire ci-dessus</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-semibold text-gray-800">
            Entrées en attente ({entries.length})
          </CardTitle>
          <div className="text-sm text-gray-600">
            Total: <span className="font-semibold">{getTotalHours()}h</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Entries list */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {entries.map((entry) => (
            <div
              key={entry.tempId}
              className="border border-gray-200 rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
            >
              <div className="flex justify-between items-start mb-2">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-gray-900">{entry.activity}</h4>
                    <Badge
                      variant={entry.remoteWork ? 'default' : 'secondary'}
                      className={cn(
                        'text-xs',
                        entry.remoteWork
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      )}
                    >
                      {entry.remoteWork ? (
                        <>
                          <Home className="w-3 h-3 mr-1" />
                          Télétravail
                        </>
                      ) : (
                        <>
                          <MapPin className="w-3 h-3 mr-1" />
                          Sur site
                        </>
                      )}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-1">
                    {formatDate(entry.date)}
                  </p>
                  {entry.comment && (
                    <p className="text-sm text-gray-700 italic">"{entry.comment}"</p>
                  )}
                </div>
                <div className="flex items-center gap-2 ml-4">
                  <div className="text-right">
                    <div className="flex items-center text-sm font-medium text-gray-900">
                      <Clock className="w-4 h-4 mr-1" />
                      {entry.hours}h
                    </div>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditEntry(entry)}
                      className="h-8 w-8 p-0 text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onRemoveEntry(entry.tempId)}
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-800 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Submit button */}
        <div className="pt-4 border-t border-gray-200">
          <Button
            onClick={onSubmitAll}
            disabled={loading}
            className="w-full bg-green-600 hover:bg-green-700 text-white"
          >
            <Save className="mr-2 h-4 w-4" />
            {loading ? 'Enregistrement...' : `Enregistrer ${entries.length} entrée(s)`}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
