import { IconDownload, IconPlus } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { useTimesheet } from '../context/timesheet-context'

export function TimesheetPrimaryButtons() {
  const { setOpen } = useTimesheet()
  
  return (
    <div className='flex gap-2'>
      <Button
        variant='outline'
        className='space-x-1'
        onClick={() => setOpen('import')}
      >
        <span>Import</span> <IconDownload size={18} />
      </Button>
      <Button className='space-x-1' onClick={() => setOpen('create')}>
        <span>Add Entry</span> <IconPlus size={18} />
      </Button>
    </div>
  )
}
