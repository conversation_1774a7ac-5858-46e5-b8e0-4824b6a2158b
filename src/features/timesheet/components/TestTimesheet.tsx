import { Clock } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'

export function TestTimesheet() {
  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6 rounded-t-xl shadow-md">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold mb-2 flex items-center">
                <Clock className="mr-3 h-8 w-8" />
                Test Timesheet
              </h1>
              <p className="text-blue-100 text-sm sm:text-base">
                Testing the timesheet route
              </p>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="bg-white rounded-b-xl shadow-md">
          <div className="p-6">
            <Card>
              <CardHeader>
                <CardTitle>Timesheet Route Test</CardTitle>
              </CardHeader>
              <CardContent>
                <p>If you can see this, the timesheet route is working correctly!</p>
                <p className="mt-2 text-sm text-gray-600">
                  The 500 error was likely due to a circular dependency in the useTimesheet hook.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
