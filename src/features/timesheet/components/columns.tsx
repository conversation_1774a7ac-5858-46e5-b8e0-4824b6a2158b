import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { IconDots, IconEdit, IconTrash } from '@tabler/icons-react'
import { TimeEntry } from '../data/schema'
import { useTimesheet } from '../context/timesheet-context'

export const columns: ColumnDef<TimeEntry>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'date',
    header: 'Date',
    cell: ({ row }) => {
      const date = new Date(row.getValue('date'))
      return (
        <div className='font-medium'>
          {date.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric',
          })}
        </div>
      )
    },
  },
  {
    accessorKey: 'hours',
    header: 'Hours',
    cell: ({ row }) => {
      const hours = parseFloat(row.getValue('hours'))
      return (
        <div className='font-medium'>
          {hours}h
        </div>
      )
    },
  },
  {
    accessorKey: 'location',
    header: 'Location',
    cell: ({ row }) => {
      const location = row.getValue('location') as string
      return (
        <Badge variant={location === 'home' ? 'secondary' : 'default'}>
          {location === 'home' ? '🏠 Home' : '🏢 Office'}
        </Badge>
      )
    },
  },
  {
    accessorKey: 'client',
    header: 'Client',
    cell: ({ row }) => (
      <div className='font-medium'>{row.getValue('client')}</div>
    ),
  },
  {
    accessorKey: 'project',
    header: 'Project',
    cell: ({ row }) => {
      const project = row.getValue('project') as string
      return project ? (
        <div className='text-muted-foreground'>{project}</div>
      ) : (
        <div className='text-muted-foreground italic'>No project</div>
      )
    },
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const description = row.getValue('description') as string
      return description ? (
        <div className='max-w-[200px] truncate text-muted-foreground'>
          {description}
        </div>
      ) : (
        <div className='text-muted-foreground italic'>No description</div>
      )
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ({ row }) => {
      const timeEntry = row.original
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const { setOpen, setCurrentRow } = useTimesheet()

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <IconDots className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                setCurrentRow(timeEntry)
                setOpen('update')
              }}
            >
              <IconEdit className='mr-2 h-4 w-4' />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                setCurrentRow(timeEntry)
                setOpen('delete')
              }}
              className='text-destructive'
            >
              <IconTrash className='mr-2 h-4 w-4' />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
