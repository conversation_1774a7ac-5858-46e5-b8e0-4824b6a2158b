import { Chevron<PERSON>eft, ChevronRight, CalendarDays, Eye } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { getDaysInMonth, getFirstDayOfMonth, getMonthName } from '../utils/date-utils'

interface CalendarProps {
  selectedMonth: number
  selectedYear: number
  selectedDay: number | null
  daysWithEntries: number[]
  onSelectDay: (day: number) => void
  onPreviousMonth: () => void
  onNextMonth: () => void
  onViewAllMonth: () => void
}

const WEEKDAYS = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam']

export function Calendar({
  selectedMonth,
  selectedYear,
  selectedDay,
  daysWithEntries,
  onSelectDay,
  onPreviousMonth,
  onNextMonth,
  onViewAllMonth,
}: CalendarProps) {
  const today = new Date()
  const currentMonth = today.getMonth()
  const currentYear = today.getFullYear()
  const currentDay = today.getDate()



  const daysInMonth = getDaysInMonth(selectedYear, selectedMonth)
  const firstDayOfMonth = getFirstDayOfMonth(selectedYear, selectedMonth)

  // Generate calendar days
  const generateCalendarDays = () => {
    const days: (number | null)[] = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(null)
    }

    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day)
    }

    return days
  }

  const calendarDays = generateCalendarDays()

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CalendarDays className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-800">
              {getMonthName(selectedMonth)} {selectedYear}
            </h3>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={onPreviousMonth}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onNextMonth}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Weekday headers */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {WEEKDAYS.map((weekday) => (
            <div
              key={weekday}
              className="h-8 flex items-center justify-center text-sm font-medium text-gray-500"
            >
              {weekday}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((day, index) => {
            if (day === null) {
              return <div key={index} className="h-10" />
            }

            const isToday =
              day === currentDay &&
              selectedMonth === currentMonth &&
              selectedYear === currentYear

            const isSelected = day === selectedDay
            const hasEntries = daysWithEntries.includes(day)

            return (
              <Button
                key={day}
                variant="ghost"
                onClick={() => onSelectDay(day)}
                className={cn(
                  'h-10 w-full p-0 relative font-normal',
                  isToday && 'bg-blue-50 text-blue-600 font-medium',
                  isSelected && 'bg-blue-600 text-white hover:bg-blue-700',
                  !isSelected && hasEntries && 'font-semibold text-blue-600',
                  !isSelected && !isToday && 'hover:bg-gray-100'
                )}
              >
                {day}
                {hasEntries && !isSelected && (
                  <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-600 rounded-full" />
                )}
              </Button>
            )
          })}
        </div>

        {/* Selected day info */}
        {selectedDay && (
          <div className="mt-4 pt-3 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <h4 className="font-medium text-gray-800">
                {selectedDay} {getMonthName(selectedMonth)} {selectedYear}
              </h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={onViewAllMonth}
                className="text-blue-600 hover:text-blue-800 h-auto p-1"
              >
                <Eye className="h-4 w-4 mr-1" />
                Voir tout le mois
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
