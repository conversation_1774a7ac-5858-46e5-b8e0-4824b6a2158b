import { Clock, Calendar, PlusCircle } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export function DebugTimesheet() {
  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6 rounded-t-xl shadow-md">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold mb-2 flex items-center">
                <Clock className="mr-3 h-8 w-8" />
                Debug Timesheet
              </h1>
              <p className="text-blue-100 text-sm sm:text-base">
                Testing timesheet without API calls
              </p>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="bg-white rounded-b-xl shadow-md">
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left column - Simple form */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <PlusCircle className="mr-2 h-5 w-5 text-blue-600" />
                      Test Form
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="test-date">Date</Label>
                      <Input
                        id="test-date"
                        type="date"
                        defaultValue={new Date().toISOString().split('T')[0]}
                      />
                    </div>
                    <div>
                      <Label htmlFor="test-activity">Activity</Label>
                      <Input
                        id="test-activity"
                        placeholder="Test activity"
                      />
                    </div>
                    <div>
                      <Label htmlFor="test-hours">Hours</Label>
                      <Input
                        id="test-hours"
                        type="number"
                        placeholder="8"
                        step="0.5"
                      />
                    </div>
                    <Button className="w-full">
                      Test Add Entry
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Right column - Simple calendar */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Calendar className="mr-2 h-5 w-5 text-blue-600" />
                      Test Calendar
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <Calendar className="mx-auto h-16 w-16 text-blue-300 mb-4" />
                      <p className="text-gray-600">
                        Calendar component would go here
                      </p>
                      <p className="text-sm text-gray-500 mt-2">
                        Current month: {new Date().toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' })}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Status */}
            <div className="mt-6">
              <Card className="border-green-200 bg-green-50">
                <CardContent className="pt-6">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                    <div>
                      <p className="font-medium text-green-800">
                        Timesheet Route Working
                      </p>
                      <p className="text-sm text-green-600">
                        If you can see this page, the route is properly configured and working.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Debug info */}
            <div className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Debug Information</CardTitle>
                </CardHeader>
                <CardContent className="text-sm space-y-2">
                  <p><strong>Route:</strong> /_authenticated/timesheet/</p>
                  <p><strong>Component:</strong> DebugTimesheet</p>
                  <p><strong>Status:</strong> ✅ Loaded successfully</p>
                  <p><strong>Authentication:</strong> Required (under _authenticated layout)</p>
                  <p><strong>Next Step:</strong> Switch to full Timesheet component</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
