import { toast } from '@/hooks/use-toast'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { useTimesheet } from '../context/timesheet-context'
import { TimesheetMutateDrawer } from './timesheet-mutate-drawer'
import { TimesheetImportDialog } from './timesheet-import-dialog'

export function TimesheetDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useTimesheet()

  const handleDelete = () => {
    if (!currentRow) return
    
    // TODO: Implement actual delete API call
    console.log('Deleting time entry:', currentRow.id)
    
    toast({
      title: 'Time entry deleted',
      description: `Successfully deleted time entry for ${currentRow.hours} hours.`,
    })
    
    setCurrentRow(null)
    setOpen(null)
  }

  return (
    <>
      <TimesheetMutateDrawer
        key='timesheet-create'
        open={open === 'create'}
        onOpenChange={() => setOpen('create')}
      />

      <TimesheetImportDialog
        key='timesheet-import'
        open={open === 'import'}
        onOpenChange={() => setOpen('import')}
      />

      {currentRow && (
        <>
          <TimesheetMutateDrawer
            key={`timesheet-update-${currentRow.id}`}
            open={open === 'update'}
            onOpenChange={() => {
              setOpen('update')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            currentRow={currentRow}
          />

          <ConfirmDialog
            open={open === 'delete'}
            onOpenChange={() => setOpen('delete')}
            handleConfirm={handleDelete}
            title='Delete Time Entry'
            desc={`Are you sure you want to delete this time entry for ${currentRow.hours} hours on ${new Date(currentRow.date).toLocaleDateString()}? This action cannot be undone.`}
          />
        </>
      )}
    </>
  )
}
