import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { SelectDropdown } from '@/components/select-dropdown'
import { DatePickerWithRange } from '@/components/ui/date-picker-with-range'
import { timeEntryFormSchema, TimeEntryForm } from '../data/schema'
import { mockClients, mockProjects } from '../data/mock-data'
import { TimeEntry } from '../data/schema'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: TimeEntry
}

export function TimesheetMutateDrawer({ open, onOpenChange, currentRow }: Props) {
  const isUpdate = !!currentRow

  const form = useForm<TimeEntryForm>({
    resolver: zodResolver(timeEntryFormSchema),
    defaultValues: currentRow ? {
      dateRange: {
        from: new Date(currentRow.date),
        to: new Date(currentRow.date), // Single day as range
      },
      hours: currentRow.hours.toString(),
      location: currentRow.location,
      client: currentRow.client,
      project: currentRow.project || '',
      description: currentRow.description || '',
    } : {
      dateRange: {
        from: new Date(),
        to: new Date(), // Default to single day
      },
      hours: '',
      location: 'office',
      client: '',
      project: '',
      description: '',
    },
  })

  const selectedClient = form.watch('client')
  const availableProjects = mockProjects.filter(
    project => project.clientId === mockClients.find(c => c.name === selectedClient)?.id
  )

  const onSubmit = (data: TimeEntryForm) => {
    // TODO: Implement actual API call
    console.log('Submitting time entry:', data)

    const dateRangeText = data.dateRange.to && data.dateRange.to.getTime() !== data.dateRange.from.getTime()
      ? `from ${data.dateRange.from.toLocaleDateString()} to ${data.dateRange.to.toLocaleDateString()}`
      : `on ${data.dateRange.from.toLocaleDateString()}`

    onOpenChange(false)
    form.reset()

    toast({
      title: isUpdate ? 'Time entry updated' : 'Time entry created',
      description: `Successfully ${isUpdate ? 'updated' : 'created'} time entry for ${data.hours} hours ${dateRangeText}.`,
    })
  }

  return (
    <Sheet
      open={open}
      onOpenChange={(v) => {
        onOpenChange(v)
        if (!v) form.reset()
      }}
    >
      <SheetContent className='flex flex-col'>
        <SheetHeader className='text-left'>
          <SheetTitle>{isUpdate ? 'Update' : 'Add'} Time Entry</SheetTitle>
          <SheetDescription>
            {isUpdate
              ? 'Update the time entry details below.'
              : 'Add a new time entry by filling out the form below.'}
            Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form
            id='timesheet-form'
            onSubmit={form.handleSubmit(onSubmit)}
            className='flex-1 space-y-6'
          >
            <FormField
              control={form.control}
              name='dateRange'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date Range</FormLabel>
                  <FormControl>
                    <DatePickerWithRange
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Select date range (or single day)"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='hours'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Hours Worked</FormLabel>
                  <FormControl>
                    <Input
                      type='number'
                      step='0.5'
                      min='0'
                      max='24'
                      placeholder='8.0'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='location'
              render={({ field }) => (
                <FormItem className='space-y-3'>
                  <FormLabel>Work Location</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className='flex flex-col space-y-1'
                    >
                      <FormItem className='flex items-center space-x-3 space-y-0'>
                        <FormControl>
                          <RadioGroupItem value='office' />
                        </FormControl>
                        <FormLabel className='font-normal'>
                          🏢 Office
                        </FormLabel>
                      </FormItem>
                      <FormItem className='flex items-center space-x-3 space-y-0'>
                        <FormControl>
                          <RadioGroupItem value='home' />
                        </FormControl>
                        <FormLabel className='font-normal'>
                          🏠 Home/Remote
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='client'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client</FormLabel>
                  <SelectDropdown
                    defaultValue={field.value}
                    onValueChange={(value) => {
                      field.onChange(value)
                      // Reset project when client changes
                      form.setValue('project', '')
                    }}
                    placeholder='Select a client'
                    items={mockClients.map(client => ({
                      label: client.name,
                      value: client.name,
                    }))}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedClient && availableProjects.length > 0 && (
              <FormField
                control={form.control}
                name='project'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project (Optional)</FormLabel>
                    <SelectDropdown
                      defaultValue={field.value}
                      onValueChange={field.onChange}
                      placeholder='Select a project'
                      items={availableProjects.map(project => ({
                        label: project.name,
                        value: project.name,
                      }))}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Describe what you worked on...'
                      className='resize-none'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>

        <SheetFooter className='gap-2'>
          <SheetClose asChild>
            <Button variant='outline'>Cancel</Button>
          </SheetClose>
          <Button form='timesheet-form' type='submit'>
            {isUpdate ? 'Update' : 'Save'} Entry
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
