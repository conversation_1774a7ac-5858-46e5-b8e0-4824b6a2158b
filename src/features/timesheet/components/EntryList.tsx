import { Clock, MapPin, Home, RefreshCw, Calendar } from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { cn } from '@/lib/utils'
import type { TimeEntry } from '../types'

interface EntryListProps {
  entries: TimeEntry[]
  selectedDay: number | null
  selectedMonth: number
  selectedYear: number
  loading?: boolean
}

export function EntryList({
  entries,
  selectedDay,
  selectedMonth,
  selectedYear,
  loading = false,
}: EntryListProps) {
  const getMonthName = (month: number) => {
    const date = new Date()
    date.setMonth(month)
    return date.toLocaleString('fr-FR', { month: 'long' })
  }

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }
    return new Date(dateString).toLocaleDateString('fr-FR', options)
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // Group entries by date
  const groupedEntries = entries.reduce((groups, entry) => {
    const date = entry.date
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(entry)
    return groups
  }, {} as Record<string, TimeEntry[]>)

  const getTitle = () => {
    if (selectedDay) {
      return `Entrées du ${selectedDay} ${getMonthName(selectedMonth)}`
    }
    return `Entrées de ${getMonthName(selectedMonth)} ${selectedYear}`
  }

  const getEmptyMessage = () => {
    if (selectedDay) {
      return `Aucune entrée de temps trouvée pour le ${selectedDay} ${getMonthName(selectedMonth)}`
    }
    return `Aucune entrée de temps trouvée pour ${getMonthName(selectedMonth)} ${selectedYear}`
  }

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-4">
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg font-semibold text-gray-800">
              {getTitle()}
            </CardTitle>
            <RefreshCw className="animate-spin text-blue-600 h-5 w-5" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-20 w-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
          <Calendar className="mr-2 h-5 w-5 text-blue-600" />
          {getTitle()}
        </CardTitle>
      </CardHeader>

      <CardContent>
        {entries.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Clock className="mx-auto h-12 w-12 text-gray-300 mb-3" />
            <p className="text-base">{getEmptyMessage()}</p>
            <p className="text-sm mt-1">Ajoutez vos premières entrées avec le formulaire</p>
          </div>
        ) : (
          <div className="space-y-6 max-h-[calc(100vh-300px)] overflow-y-auto">
            {Object.entries(groupedEntries)
              .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
              .map(([date, dateEntries]) => (
                <div key={date} className="space-y-3">
                  {/* Date header */}
                  <div className="flex items-center justify-between border-b border-gray-200 pb-2">
                    <h3 className="font-medium text-gray-900">
                      {formatDate(date)}
                    </h3>
                    <div className="text-sm text-gray-600">
                      {dateEntries.reduce((total, entry) => total + entry.hours, 0)}h total
                    </div>
                  </div>

                  {/* Entries for this date */}
                  <div className="space-y-3">
                    {dateEntries
                      .sort((a, b) => new Date(b.createdAt || b.date).getTime() - new Date(a.createdAt || a.date).getTime())
                      .map((entry) => (
                        <div
                          key={entry.id}
                          className="border border-gray-200 rounded-lg p-4 bg-white hover:bg-gray-50 transition-colors"
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h4 className="font-medium text-gray-900">
                                  {entry.activity}
                                </h4>
                                <Badge
                                  variant={entry.remoteWork ? 'default' : 'secondary'}
                                  className={cn(
                                    'text-xs',
                                    entry.remoteWork
                                      ? 'bg-blue-100 text-blue-800'
                                      : 'bg-gray-100 text-gray-800'
                                  )}
                                >
                                  {entry.remoteWork ? (
                                    <>
                                      <Home className="w-3 h-3 mr-1" />
                                      Télétravail
                                    </>
                                  ) : (
                                    <>
                                      <MapPin className="w-3 h-3 mr-1" />
                                      Sur site
                                    </>
                                  )}
                                </Badge>
                              </div>

                              {entry.comment && (
                                <p className="text-sm text-gray-700 mb-2 italic">
                                  "{entry.comment}"
                                </p>
                              )}

                              {entry.createdAt && (
                                <p className="text-xs text-gray-500">
                                  Créé le {formatTime(entry.createdAt)}
                                </p>
                              )}
                            </div>

                            <div className="text-right ml-4">
                              <div className="flex items-center text-lg font-semibold text-gray-900">
                                <Clock className="w-5 h-5 mr-1 text-blue-600" />
                                {entry.hours}h
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
