import { Table } from '@tanstack/react-table'
import { IconX } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { DatePickerWithRange } from '@/components/ui/date-picker-with-range'
import { DataTableViewOptions } from './data-table-view-options'
import { DataTableFacetedFilter } from './data-table-faceted-filter'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
}

export function DataTableToolbar<TData>({
  table,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  const locationOptions = [
    {
      label: '🏢 Office',
      value: 'office',
    },
    {
      label: '🏠 Home',
      value: 'home',
    },
  ]

  const clientOptions = [
    {
      label: 'Acme Corporation',
      value: 'Acme Corporation',
    },
    {
      label: 'TechStart Inc.',
      value: 'TechStart Inc.',
    },
    {
      label: 'Global Solutions Ltd.',
      value: 'Global Solutions Ltd.',
    },
    {
      label: 'Innovation Labs',
      value: 'Innovation Labs',
    },
  ]

  return (
    <div className='flex flex-col space-y-4'>
      <div className='flex items-center justify-between'>
        <div className='flex flex-1 items-center space-x-2'>
          <Input
            placeholder='Filter by description...'
            value={(table.getColumn('description')?.getFilterValue() as string) ?? ''}
            onChange={(event) =>
              table.getColumn('description')?.setFilterValue(event.target.value)
            }
            className='h-8 w-[150px] lg:w-[250px]'
          />
          {table.getColumn('location') && (
            <DataTableFacetedFilter
              column={table.getColumn('location')}
              title='Location'
              options={locationOptions}
            />
          )}
          {table.getColumn('client') && (
            <DataTableFacetedFilter
              column={table.getColumn('client')}
              title='Client'
              options={clientOptions}
            />
          )}
          {isFiltered && (
            <Button
              variant='ghost'
              onClick={() => table.resetColumnFilters()}
              className='h-8 px-2 lg:px-3'
            >
              Reset
              <IconX className='ml-2 h-4 w-4' />
            </Button>
          )}
        </div>
        <DataTableViewOptions table={table} />
      </div>

      {/* Date Range Filter */}
      <div className='flex items-center space-x-2'>
        <span className='text-sm font-medium'>Filter by date range:</span>
        <DatePickerWithRange className='w-auto' />
      </div>
    </div>
  )
}
