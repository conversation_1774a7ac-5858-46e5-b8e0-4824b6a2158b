import { z } from 'zod'

export const timeEntrySchema = z.object({
  id: z.string(),
  date: z.string(),
  hours: z.number().positive().max(24),
  location: z.enum(['office', 'home']),
  client: z.string().min(1),
  project: z.string().optional(),
  description: z.string().optional(),
  consultantId: z.string(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
})

export const timeEntryFormSchema = z.object({
  date: z.string().min(1, 'Date is required'),
  hours: z.string()
    .min(1, 'Hours is required')
    .refine((val) => {
      const num = parseFloat(val)
      return !isNaN(num) && num > 0 && num <= 24
    }, 'Hours must be a positive number up to 24'),
  location: z.enum(['office', 'home'], {
    required_error: 'Please select a work location',
  }),
  client: z.string().min(1, 'Please select a client'),
  project: z.string().optional(),
  description: z.string().optional(),
})

export const clientSchema = z.object({
  id: z.string(),
  name: z.string(),
  code: z.string().optional(),
})

export const projectSchema = z.object({
  id: z.string(),
  name: z.string(),
  clientId: z.string(),
  code: z.string().optional(),
})

export type TimeEntry = z.infer<typeof timeEntrySchema>
export type TimeEntryForm = z.infer<typeof timeEntryFormSchema>
export type Client = z.infer<typeof clientSchema>
export type Project = z.infer<typeof projectSchema>
