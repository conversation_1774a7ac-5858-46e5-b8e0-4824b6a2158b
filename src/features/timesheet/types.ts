export interface TimeEntry {
  id?: string
  date: string
  activity: string
  comment?: string
  hours: number
  remoteWork: boolean
  consultantId: string
  createdAt?: string
  updatedAt?: string
}

export interface PendingTimeEntry extends Omit<TimeEntry, 'id' | 'consultantId'> {
  tempId: number
}

export interface TimeEntryFormData {
  date: string
  activity: string
  comment: string
  hours: string
  remoteWork: boolean
}

export interface ConsultantInfo {
  id: string
  name: string
  email: string
  // Add other consultant properties as needed
}

export interface TimeEntryFilters {
  month: number
  year: number
  day?: number
}

export interface TimeEntryStats {
  totalHours: number
  remoteHours: number
  onsiteHours: number
  totalDays: number
}

export interface CalendarDay {
  day: number
  hasEntries: boolean
  isToday: boolean
  isSelected: boolean
}

export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

export type ViewMode = 'month' | 'day'

export interface UseTimesheetState {
  // Form state
  formData: TimeEntryFormData
  
  // Entries state
  pendingEntries: PendingTimeEntry[]
  savedEntries: TimeEntry[]
  
  // UI state
  loading: boolean
  error: string
  message: string
  
  // Calendar state
  selectedMonth: number
  selectedYear: number
  selectedDay: number | null
  viewMode: ViewMode
  daysWithEntries: number[]
  
  // Consultant state
  consultantId: string | null
}

export interface UseTimesheetActions {
  // Form actions
  updateFormData: (data: Partial<TimeEntryFormData>) => void
  resetForm: () => void
  
  // Entry actions
  addEntryToPending: () => void
  removePendingEntry: (tempId: number) => void
  editPendingEntry: (entry: PendingTimeEntry) => void
  submitAllEntries: () => Promise<void>
  
  // Calendar actions
  selectDay: (day: number) => void
  goToPreviousMonth: () => void
  goToNextMonth: () => void
  setToday: () => void
  setViewMode: (mode: ViewMode) => void
  
  // Data actions
  fetchTimeEntries: () => Promise<void>
  refreshData: () => Promise<void>
  
  // UI actions
  clearMessage: () => void
  clearError: () => void
}
