export interface TimeEntry {
  id: string
  date: string
  hours: number
  location: 'office' | 'home'
  client: string
  project?: string
  description?: string
  consultantId: string
  createdAt?: string
  updatedAt?: string
}

export interface Client {
  id: string
  name: string
  code?: string
}

export interface Project {
  id: string
  name: string
  clientId: string
  code?: string
}

export interface ConsultantInfo {
  id: string
  name: string
  email: string
}

export interface TimeEntryFormData {
  dateRange: {
    from: Date
    to?: Date
  }
  hours: string
  location: 'office' | 'home'
  client: string
  project?: string
  description?: string
}

export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}
