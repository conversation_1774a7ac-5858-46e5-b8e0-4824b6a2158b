import { Clock, RefreshCw, AlertCircle, CheckCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useTimesheet } from './hooks/useTimesheet'
import { Calendar } from './components/Calendar'
import { TimeEntryForm } from './components/TimeEntryForm'
import { PendingEntries } from './components/PendingEntries'
import { EntryList } from './components/EntryList'

export function Timesheet() {
  const {
    // Form state
    formData,
    updateFormData,
    resetForm,
    
    // Entries state
    pendingEntries,
    savedEntries,
    
    // UI state
    loading,
    error,
    message,
    
    // Calendar state
    selectedMonth,
    selectedYear,
    selectedDay,
    viewMode,
    daysWithEntries,
    
    // Actions
    addEntryToPending,
    removePendingEntry,
    editPendingEntry,
    submitAllEntries,
    selectDay,
    goToPreviousMonth,
    goToNextMonth,
    setToday,
    setViewMode,
    refreshData,
    clearMessage,
    clearError,
  } = useTimesheet()

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6 rounded-t-xl shadow-md">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold mb-2 flex items-center">
                <Clock className="mr-3 h-8 w-8" />
                Gestion des Temps
              </h1>
              <p className="text-blue-100 text-sm sm:text-base">
                Enregistrez et suivez vos heures de travail avec une interface intuitive
              </p>
            </div>
            <Button
              onClick={refreshData}
              variant="secondary"
              size="sm"
              disabled={loading}
              className="bg-blue-700 hover:bg-blue-800 text-white border-blue-600"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Actualiser
            </Button>
          </div>
        </div>

        {/* Messages */}
        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex justify-between items-center">
              {error}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="text-red-600 hover:text-red-800 h-auto p-1"
              >
                ×
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {message && (
          <Alert className="mt-4 border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="flex justify-between items-center text-green-800">
              {message}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearMessage}
                className="text-green-600 hover:text-green-800 h-auto p-1"
              >
                ×
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Main content */}
        <div className="bg-white rounded-b-xl shadow-md">
          <div className="p-6">
            {/* Calendar section */}
            <div className="mb-6">
              <Calendar
                selectedMonth={selectedMonth}
                selectedYear={selectedYear}
                selectedDay={selectedDay}
                daysWithEntries={daysWithEntries}
                onSelectDay={selectDay}
                onPreviousMonth={goToPreviousMonth}
                onNextMonth={goToNextMonth}
                onViewAllMonth={() => setViewMode('month')}
              />
            </div>

            {/* Form and entries grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left column - Form and pending entries */}
              <div className="space-y-6">
                <TimeEntryForm
                  formData={formData}
                  onFormDataChange={updateFormData}
                  onSubmit={addEntryToPending}
                  onSetToday={setToday}
                  loading={loading}
                />

                <PendingEntries
                  entries={pendingEntries}
                  onRemoveEntry={removePendingEntry}
                  onEditEntry={editPendingEntry}
                  onSubmitAll={submitAllEntries}
                  loading={loading}
                />
              </div>

              {/* Right column - Saved entries */}
              <div>
                <EntryList
                  entries={savedEntries}
                  selectedDay={selectedDay}
                  selectedMonth={selectedMonth}
                  selectedYear={selectedYear}
                  loading={loading}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
