import { Clock, RefreshCw, Alert<PERSON>ircle, CheckCircle, Plus, Download } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { useTimesheet } from './hooks/useTimesheet'
import { Calendar } from './components/Calendar'
import { TimeEntryForm } from './components/TimeEntryForm'
import { PendingEntries } from './components/PendingEntries'
import { EntryList } from './components/EntryList'

export function Timesheet() {
  const {
    // Form state
    formData,
    updateFormData,

    // Entries state
    pendingEntries,
    savedEntries,

    // UI state
    loading,
    error,
    message,

    // Calendar state
    selectedMonth,
    selectedYear,
    selectedDay,
    daysWithEntries,

    // Actions
    addEntryToPending,
    removePendingEntry,
    editPendingEntry,
    submitAllEntries,
    selectDay,
    goToPreviousMonth,
    goToNextMonth,
    setToday,
    setViewMode,
    refreshData,
    clearMessage,
    clearError,
  } = useTimesheet()

  const handleQuickAddEntry = () => {
    setToday()
    // Focus on activity field after setting today's date
    setTimeout(() => {
      const activityInput = document.getElementById('activity')
      activityInput?.focus()
    }, 100)
  }

  const handleExportTimesheet = () => {
    // TODO: Implement export functionality
    console.log('Export timesheet functionality to be implemented')
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          {/* Quick Actions */}
          <Button
            onClick={handleQuickAddEntry}
            size="sm"
            className="hidden sm:flex"
          >
            <Plus className="h-4 w-4 mr-2" />
            Quick Add
          </Button>

          <Button
            onClick={refreshData}
            variant="outline"
            size="sm"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            <span className="hidden sm:inline">Refresh</span>
          </Button>

          <Button
            onClick={handleExportTimesheet}
            variant="outline"
            size="sm"
          >
            <Download className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Export</span>
          </Button>

          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Content ===== */}
      <Main>
        {/* Page Title and Description */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight flex items-center">
                <Clock className="mr-3 h-8 w-8 text-primary" />
                Time Tracking
              </h1>
              <p className="text-muted-foreground mt-2">
                Log and manage your work hours efficiently with our intuitive timesheet system.
              </p>
            </div>

            {/* Mobile Quick Actions */}
            <div className="flex sm:hidden space-x-2">
              <Button
                onClick={handleQuickAddEntry}
                size="sm"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Status Messages */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex justify-between items-center">
              {error}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="text-red-600 hover:text-red-800 h-auto p-1"
              >
                ×
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {message && (
          <Alert className="mb-6 border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="flex justify-between items-center text-green-800">
              {message}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearMessage}
                className="text-green-600 hover:text-green-800 h-auto p-1"
              >
                ×
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Calendar Section */}
        <div className="mb-8">
          <Calendar
            selectedMonth={selectedMonth}
            selectedYear={selectedYear}
            selectedDay={selectedDay}
            daysWithEntries={daysWithEntries}
            onSelectDay={selectDay}
            onPreviousMonth={goToPreviousMonth}
            onNextMonth={goToNextMonth}
            onViewAllMonth={() => setViewMode('month')}
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          {/* Left Column - Entry Management */}
          <div className="space-y-6">
            <TimeEntryForm
              formData={formData}
              onFormDataChange={updateFormData}
              onSubmit={addEntryToPending}
              onSetToday={setToday}
              loading={loading}
            />

            <PendingEntries
              entries={pendingEntries}
              onRemoveEntry={removePendingEntry}
              onEditEntry={editPendingEntry}
              onSubmitAll={submitAllEntries}
              loading={loading}
            />
          </div>

          {/* Right Column - Time Entries */}
          <div>
            <EntryList
              entries={savedEntries}
              selectedDay={selectedDay}
              selectedMonth={selectedMonth}
              selectedYear={selectedYear}
              loading={loading}
            />
          </div>
        </div>
      </Main>
    </>
  )
}
