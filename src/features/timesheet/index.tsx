import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { columns } from './components/columns'
import { DataTable } from './components/data-table'
import { TimesheetDialogs } from './components/timesheet-dialogs'
import { TimesheetPrimaryButtons } from './components/timesheet-primary-buttons'
import TimesheetProvider from './context/timesheet-context'
import { mockTimeEntries } from './data/mock-data'

export default function Timesheet() {
  return (
    <TimesheetProvider>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between gap-x-4 space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Time Tracking</h2>
            <p className='text-muted-foreground'>
              Log and manage your work hours efficiently with our timesheet system.
            </p>
          </div>
          <TimesheetPrimaryButtons />
        </div>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
          <DataTable data={mockTimeEntries} columns={columns} />
        </div>
      </Main>

      <TimesheetDialogs />
    </TimesheetProvider>
  )
}
