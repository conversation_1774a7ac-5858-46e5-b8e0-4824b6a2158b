import React, { useState } from 'react'
import useDialogState from '@/hooks/use-dialog-state'
import { TimeEntry } from '../data/schema'

type TimesheetDialogType = 'create' | 'update' | 'delete' | 'import'

interface TimesheetContextType {
  open: TimesheetDialogType | null
  setOpen: (str: TimesheetDialogType | null) => void
  currentRow: TimeEntry | null
  setCurrentRow: React.Dispatch<React.SetStateAction<TimeEntry | null>>
}

const TimesheetContext = React.createContext<TimesheetContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function TimesheetProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<TimesheetDialogType>(null)
  const [currentRow, setCurrentRow] = useState<TimeEntry | null>(null)
  
  return (
    <TimesheetContext value={{ open, setOpen, currentRow, setCurrentRow }}>
      {children}
    </TimesheetContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useTimesheet = () => {
  const timesheetContext = React.useContext(TimesheetContext)

  if (!timesheetContext) {
    throw new Error('useTimesheet has to be used within <TimesheetContext>')
  }

  return timesheetContext
}
