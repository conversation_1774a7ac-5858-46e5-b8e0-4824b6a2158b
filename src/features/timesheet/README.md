# Timesheet Management Feature

A comprehensive timesheet management system built with React, TypeScript, and TanStack Router. This feature allows users to track their work hours with an intuitive calendar-based interface.

## Features

### 🗓️ Calendar Interface
- Interactive monthly calendar view
- Visual indicators for days with time entries
- Day selection for detailed view
- Month navigation with previous/next controls
- "Today" quick selection

### ⏰ Time Entry Management
- Add multiple time entries for the same day
- Form validation for required fields
- Support for decimal hours (e.g., 7.5 hours)
- Remote work tracking with checkbox
- Optional comments for detailed descriptions

### 📝 Batch Operations
- Add entries to a pending list before submission
- Edit pending entries before saving
- Remove entries from pending list
- Bat<PERSON> submit all pending entries at once

### 📊 Data Visualization
- Group entries by date
- Display total hours per day
- Remote vs onsite work indicators
- Real-time statistics

### 🔄 Real-time Updates
- Automatic data refresh after submissions
- Loading states for better UX
- Error handling with user-friendly messages
- Success notifications

## Architecture

### File Structure
```
src/features/timesheet/
├── components/
│   ├── Calendar.tsx           # Interactive calendar component
│   ├── TimeEntryForm.tsx      # Form for adding time entries
│   ├── PendingEntries.tsx     # Manage pending entries
│   └── EntryList.tsx          # Display saved entries
├── hooks/
│   └── useTimesheet.ts        # Main state management hook
├── utils/
│   └── date-utils.ts          # Date utility functions
├── types.ts                   # TypeScript type definitions
├── index.tsx                  # Main timesheet component
└── README.md                  # This file
```

### Key Components

#### `useTimesheet` Hook
Central state management hook that handles:
- Form state management
- API calls for CRUD operations
- Calendar state (month, year, selected day)
- Pending entries management
- Error and loading states

#### `Calendar` Component
Interactive calendar with:
- Month view with day selection
- Visual indicators for days with entries
- Navigation controls
- Responsive design

#### `TimeEntryForm` Component
Form for adding time entries with:
- Date picker with "Today" button
- Activity description field
- Hours input with validation
- Remote work checkbox
- Comments textarea

#### `PendingEntries` Component
Manages entries before submission:
- List of pending entries
- Edit/delete functionality
- Batch submission
- Total hours calculation

#### `EntryList` Component
Displays saved entries:
- Grouped by date
- Remote/onsite indicators
- Total hours per day
- Responsive layout

## API Integration

### Endpoints Used
- `POST /v1/work-time` - Create time entry
- `GET /v1/work-time/consultant/{id}` - Get consultant's time entries

### Authentication
Uses JWT token from localStorage with automatic header injection via axios interceptors.

## State Management

### Form State
```typescript
interface TimeEntryFormData {
  date: string
  activity: string
  comment: string
  hours: string
  remoteWork: boolean
}
```

### Calendar State
```typescript
interface CalendarState {
  selectedMonth: number
  selectedYear: number
  selectedDay: number | null
  viewMode: 'month' | 'day'
  daysWithEntries: number[]
}
```

### Entry State
```typescript
interface EntryState {
  pendingEntries: PendingTimeEntry[]
  savedEntries: TimeEntry[]
  loading: boolean
  error: string
  message: string
}
```

## Usage

### Basic Usage
```tsx
import { Timesheet } from '@/features/timesheet'

function App() {
  return <Timesheet />
}
```

### Navigation Integration
The timesheet is accessible via the sidebar navigation at `/timesheet`.

## Performance Optimizations

### Code Splitting
- Lazy-loaded route for optimal bundle size
- Component-level code splitting

### State Optimization
- `useCallback` for event handlers
- Memoized calculations
- Efficient re-renders with proper dependencies

### API Optimization
- Batch operations for multiple entries
- Efficient data filtering on client-side
- Proper error handling and retry logic

## Accessibility

### Keyboard Navigation
- Full keyboard support for calendar navigation
- Tab order optimization
- Focus management

### Screen Readers
- Proper ARIA labels
- Semantic HTML structure
- Descriptive button labels

### Visual Accessibility
- High contrast colors
- Clear visual indicators
- Responsive design for all screen sizes

## Error Handling

### Form Validation
- Required field validation
- Number format validation for hours
- Date format validation

### API Error Handling
- Network error handling
- Authentication error handling
- User-friendly error messages
- Automatic retry for failed requests

### User Feedback
- Loading states during API calls
- Success messages for completed actions
- Error alerts with clear descriptions
- Toast notifications for important events

## Testing Considerations

### Unit Tests
- Hook testing with React Testing Library
- Component testing for user interactions
- Utility function testing

### Integration Tests
- API integration testing
- Route testing
- End-to-end user flows

### Accessibility Tests
- Screen reader compatibility
- Keyboard navigation testing
- Color contrast validation

## Future Enhancements

### Planned Features
- Export functionality (PDF, Excel)
- Time entry templates
- Bulk edit operations
- Advanced filtering and search
- Time tracking with start/stop functionality
- Integration with project management tools

### Performance Improvements
- Virtual scrolling for large datasets
- Offline support with service workers
- Real-time collaboration features
- Advanced caching strategies

## Dependencies

### Core Dependencies
- React 19+
- TypeScript 5+
- TanStack Router
- Axios for API calls
- Lucide React for icons

### UI Dependencies
- Radix UI components
- Tailwind CSS for styling
- shadcn/ui component library

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

When contributing to this feature:
1. Follow the existing code patterns
2. Add proper TypeScript types
3. Include error handling
4. Write comprehensive tests
5. Update documentation
6. Ensure accessibility compliance
