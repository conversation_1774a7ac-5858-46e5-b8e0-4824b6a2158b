import { useState, useEffect, useCallback, useRef } from 'react'
import { toast } from '@/hooks/use-toast'
import {
  getConsultantTimeEntries,
  batchCreateTimeEntries,
  getConsultantInfo,
  type CreateTimeEntryData,
} from '@/services/timesheet'
import type {
  TimeEntry,
  PendingTimeEntry,
  TimeEntryFormData,
  UseTimesheetState,
  UseTimesheetActions,
  ViewMode,
} from '../types'

const defaultFormData: TimeEntryFormData = {
  date: '',
  activity: '',
  comment: '',
  hours: '',
  remoteWork: false,
}

export const useTimesheet = () => {
  // State
  const [state, setState] = useState<UseTimesheetState>(() => {
    const today = new Date()
    return {
      formData: defaultFormData,
      pendingEntries: [],
      savedEntries: [],
      loading: false,
      error: '',
      message: '',
      selectedMonth: today.getMonth(),
      selectedYear: today.getFullYear(),
      selectedDay: null,
      viewMode: 'month' as ViewMode,
      daysWithEntries: [],
      consultantId: null,
    }
  })

  // Helper function to update state
  const updateState = useCallback((updates: Partial<UseTimesheetState>) => {
    setState(prev => ({ ...prev, ...updates }))
  }, [])

  // Initialize consultant info
  useEffect(() => {
    const consultantInfo = getConsultantInfo()
    if (consultantInfo) {
      updateState({ consultantId: consultantInfo.id })
    } else {
      updateState({ error: 'Consultant information not available' })
    }
  }, [updateState])

  // Actions
  const updateFormData = useCallback((data: Partial<TimeEntryFormData>) => {
    updateState({
      formData: { ...state.formData, ...data }
    })
  }, [state.formData, updateState])

  const resetForm = useCallback(() => {
    updateState({ formData: defaultFormData })
  }, [updateState])

  // Fetch time entries function
  const fetchTimeEntries = async () => {
    const { consultantId, selectedMonth, selectedYear, selectedDay } = state
    if (!consultantId) return

    updateState({ loading: true, error: '' })

    try {
      const data = await getConsultantTimeEntries(consultantId)

      // Filter entries for the selected month
      const filteredEntries = data.filter(entry => {
        const entryDate = new Date(entry.date)
        return entryDate.getMonth() === selectedMonth &&
               entryDate.getFullYear() === selectedYear
      })

      // Track which days have entries
      const daysSet = new Set<number>()
      filteredEntries.forEach(entry => {
        const day = new Date(entry.date).getDate()
        daysSet.add(day)
      })

      // Further filter by day if in day view
      let entriesToShow = filteredEntries
      if (selectedDay) {
        entriesToShow = filteredEntries.filter(entry => {
          const entryDay = new Date(entry.date).getDate()
          return entryDay === selectedDay
        })
      }

      // Sort entries by date (newest first)
      entriesToShow.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

      updateState({
        savedEntries: entriesToShow,
        daysWithEntries: Array.from(daysSet),
      })
    } catch (err) {
      console.error('Error fetching time entries:', err)
      updateState({ error: 'Error fetching time entries' })
    } finally {
      updateState({ loading: false })
    }
  }

  // Fetch time entries when consultant ID or date selection changes
  useEffect(() => {
    if (state.consultantId) {
      fetchTimeEntries()
    }
  }, [state.consultantId, state.selectedMonth, state.selectedYear, state.selectedDay])

  const addEntryToPending = useCallback(() => {
    // Validate form
    if (!state.formData.date || !state.formData.activity || !state.formData.hours) {
      updateState({ error: 'Please fill in all required fields' })
      return
    }

    const hours = parseFloat(state.formData.hours)
    if (isNaN(hours) || hours <= 0) {
      updateState({ error: 'Please enter a valid number of hours' })
      return
    }

    // Create new pending entry
    const newEntry: PendingTimeEntry = {
      ...state.formData,
      tempId: Date.now(),
      hours,
    }

    updateState({
      pendingEntries: [...state.pendingEntries, newEntry],
      message: 'Entry added to list. Don\'t forget to submit your entries!',
      error: '',
    })

    resetForm()

    // Clear message after 3 seconds
    setTimeout(() => updateState({ message: '' }), 3000)
  }, [state.formData, state.pendingEntries, updateState, resetForm])

  const removePendingEntry = useCallback((tempId: number) => {
    updateState({
      pendingEntries: state.pendingEntries.filter(entry => entry.tempId !== tempId)
    })
  }, [state.pendingEntries, updateState])

  const editPendingEntry = useCallback((entry: PendingTimeEntry) => {
    updateState({
      formData: {
        ...entry,
        hours: entry.hours.toString(),
      }
    })
    removePendingEntry(entry.tempId)
  }, [updateState, removePendingEntry])

  const submitAllEntries = useCallback(async () => {
    if (state.pendingEntries.length === 0) {
      updateState({ error: 'No entries to submit' })
      return
    }

    if (!state.consultantId) {
      updateState({ error: 'Consultant ID not available' })
      return
    }

    updateState({ loading: true, error: '' })

    try {
      const entriesToSubmit: CreateTimeEntryData[] = state.pendingEntries.map(entry => ({
        date: entry.date,
        activity: entry.activity,
        comment: entry.comment || '',
        hours: entry.hours,
        remoteWork: entry.remoteWork,
        consultantId: state.consultantId!,
      }))

      await batchCreateTimeEntries(entriesToSubmit)

      updateState({
        message: `${state.pendingEntries.length} entry(ies) saved successfully!`,
        pendingEntries: [],
      })

      // Refresh entries list
      await fetchTimeEntries()

      toast({
        title: 'Success',
        description: `${state.pendingEntries.length} time entries saved successfully!`,
      })
    } catch (err) {
      console.error('Error submitting entries:', err)
      updateState({ error: 'Error saving entries' })
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save time entries. Please try again.',
      })
    } finally {
      updateState({ loading: false })
    }
  }, [state.pendingEntries, state.consultantId, updateState, fetchTimeEntries])

  // Calendar actions
  const selectDay = useCallback((day: number) => {
    updateState({
      selectedDay: day,
      viewMode: 'day',
    })
  }, [updateState])

  const goToPreviousMonth = useCallback(() => {
    if (state.selectedMonth === 0) {
      updateState({
        selectedMonth: 11,
        selectedYear: state.selectedYear - 1,
        selectedDay: null,
      })
    } else {
      updateState({
        selectedMonth: state.selectedMonth - 1,
        selectedDay: null,
      })
    }
  }, [state.selectedMonth, state.selectedYear, updateState])

  const goToNextMonth = useCallback(() => {
    if (state.selectedMonth === 11) {
      updateState({
        selectedMonth: 0,
        selectedYear: state.selectedYear + 1,
        selectedDay: null,
      })
    } else {
      updateState({
        selectedMonth: state.selectedMonth + 1,
        selectedDay: null,
      })
    }
  }, [state.selectedMonth, state.selectedYear, updateState])

  const setToday = useCallback(() => {
    const today = new Date()
    const formattedDate = today.toISOString().split('T')[0]

    updateState({
      formData: { ...state.formData, date: formattedDate },
      selectedMonth: today.getMonth(),
      selectedYear: today.getFullYear(),
      selectedDay: today.getDate(),
      viewMode: 'day',
    })
  }, [state.formData, updateState])

  const setViewMode = useCallback((mode: ViewMode) => {
    updateState({ viewMode: mode })
    if (mode === 'month') {
      updateState({ selectedDay: null })
    }
  }, [updateState])

  return {
    ...state,
    updateFormData,
    resetForm,
    addEntryToPending,
    removePendingEntry,
    editPendingEntry,
    submitAllEntries,
    fetchTimeEntries,
    refreshData: fetchTimeEntries,
    selectDay,
    goToPreviousMonth,
    goToNextMonth,
    setToday,
    setViewMode,
    clearMessage: () => updateState({ message: '' }),
    clearError: () => updateState({ error: '' }),
  }
}
