# Product Requirements Document (PRD)

## Product Title: Consultant Time & Expense Tracker

### Document Owner: <PERSON>

### Last Updated: 2025-05-27

---

## 1. Purpose

The purpose of this application is to streamline and simplify time and expense tracking for consultants. It enables consultants to log their working hours and expenses efficiently, while allowing administrators to manage users, clients, and access data across the platform.

---

## 2. Goals

* Provide an intuitive interface for consultants to log work hours and expenses.
* Support flexible data entry (single day, date range).
* Enable tracking of time off and non-working days.
* Maintain strict access control between users and admins.

---

## 3. Features

### 3.1 User Roles

**Consultant:**

* Can log time and expenses.
* Can view and edit their own data.
* Can create custom expense types.

**Admin:**

* Can view, edit, and delete all user data.
* Can create, edit, and delete consultants and clients.
* Can view reports and exports.

---

### 3.2 Time Tracking

**Functionality:**

* Consultants can log hours worked.
* Must specify:

  * Number of hours (e.g., 8.5)
  * Work location: `office` or `home`
  * Client (from a predefined list)
  * Applicable date: either a single day or a range
  * Optional: Associated project (if feature is enabled later)

**Validation:**

* Hours must be a positive number with up to two decimal places.
* Date ranges should not overlap with other entries for the same client

---

### 3.3 Expense Tracking

**Functionality:**

* Consultants can log expenses.
* Expenses can span a day or a period.
* Expense types:

  * Travel (grouped into "Trips")
  * Meals, lodging, etc.
  * Custom types defined by the user

**Each Expense includes:**

* Designation (free text)
* Amount in CHF (mandatory)
* Amount in another currency (optional)
* Exchange rate at time of transaction (if foreign amount is provided)
* Date or period covered
* Attached file (receipt, PDF, etc.)
* Optional comment

**Trips:**

* Group multiple expenses together (e.g., for a business trip).
* Must have:

  * Title
  * Start and end dates
  * Client

---

### 3.4 Non-working Days

**Functionality:**

* Consultants can log non-working days:

  * Public Holiday
  * Sick Leave
  * Paid Leave
  * Unpaid Leave
  * Other (with custom reason)

**Attributes:**

* Reason (selected from list)
* Start date and end date

---

### 3.5 Access Control

* Users can only view and manage their own data.
* Admins have unrestricted access.
* Admins can create/update/delete:

  * Users
  * Clients

---

## 4. Non-Functional Requirements

### 4.1 Usability

* Simple and responsive UI optimized for desktop and mobile use.

### 4.2 Security

* Role-based access control.
* All file uploads scanned for malware.
* Data encrypted in transit and at rest.

### 4.3 Performance

* Should support 1,000+ active consultants without performance degradation.

### 4.4 Audit & Logs

* Admins can see who created/modified each time or expense entry.

---

## 5. Future Considerations

* Project allocation and assignment.
* Timesheet generation.
* Analytics dashboard (e.g., billable vs. non-billable hours).
* Integration with accounting systems (e.g., SAP, QuickBooks).

---

## 6. Appendix

**Entities:**

* Users
* Clients
* Time Entries
* Expenses
* Trips
* Time Off
* Expense Types

**File Attachments:**

* Must support various formats

---

## 7. Stakeholders

* Product Owner
* Admin Users (internal management)
* Consultant Users (external contractors)
* Development & QA Teams

---