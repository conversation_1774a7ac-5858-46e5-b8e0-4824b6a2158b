import React, { useEffect, useState } from 'react';
import { Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend
} from 'chart.js';
import axios from 'axios';

ChartJS.register(ArcElement, Tooltip, Legend);

// Helper function to calculate percentage
const calculatePercentage = (value, total) => {
  if (total === 0) return 0;
  return ((value / total) * 100).toFixed(1);
};

const WorkChart = () => {
  const [remoteHours, setRemoteHours] = useState(0);
  const [onsiteHours, setOnsiteHours] = useState(0);

  useEffect(() => {
    const loadData = async () => {
      const token = localStorage.getItem('token');
      const consultantInfo = JSON.parse(localStorage.getItem('consultantInfo'));
      const consultantId = consultantInfo?.id;

      if (!token || !consultantId) {
        console.error('Consultant ID ou token manquant.');
        return;
      }

      try {
        const response = await axios.get(
          `https://staging.api.erp.fsli-group.com/api/v1/work-time/consultant/${consultantId}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        let totalRemote = 0;
        let totalOnsite = 0;

        response.data.forEach((item) => {
          if (item.remoteWork) {
            totalRemote += item.hours;
          } else {
            totalOnsite += item.hours;
          }
        });

        setRemoteHours(totalRemote);
        setOnsiteHours(totalOnsite);
      } catch (error) {
        console.error('Erreur lors de la récupération des données :', error);
      }
    };

    loadData();
  }, []);

  // Calculate total hours
  const totalHours = remoteHours + onsiteHours;

  // Calculate percentages
  const remotePercentage = calculatePercentage(remoteHours, totalHours);
  const onsitePercentage = calculatePercentage(onsiteHours, totalHours);

  const chartData = {
    labels: ['Télétravail', 'Présentiel'],
    datasets: [
      {
        data: [remoteHours, onsiteHours],
        backgroundColor: ['#36A2EB', '#FF6384'],
        hoverOffset: 6
      }
    ]
  };

  // Chart options with custom tooltip
  const chartOptions = {
    plugins: {
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.raw || 0;
            const total = context.dataset.data.reduce((acc, data) => acc + data, 0);
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
            return `${label}: ${percentage}%`;
          }
        }
      },
      legend: {
        position: 'bottom'
      }
    },
    maintainAspectRatio: true
  };

  return (
    <div style={{ width: '400px', margin: 'auto' }}>
      <h2 className="text-center mb-4">Répartition Télétravail vs Présentiel</h2>
      <div className="text-center mb-2 text-sm text-gray-600">
        <div>Télétravail: {remotePercentage}% ({remoteHours}h)</div>
        <div>Présentiel: {onsitePercentage}% ({onsiteHours}h)</div>
        <div className="font-medium mt-1">Total: {totalHours}h</div>
      </div>
      <Doughnut data={chartData} options={chartOptions} />
    </div>
  );
};

export default WorkChart;
