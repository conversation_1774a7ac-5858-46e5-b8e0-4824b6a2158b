import { Link, useLocation } from "react-router-dom";
import { useState } from "react";
import { 
  Menu, 
  X, 
  Home, 
  Clock, 
  Receipt, 
  CalendarDays, 
  ShieldCheck, 
  LogOut,
  ChevronRight,
  CirclePlus
} from "lucide-react";

function NavbarAdmin() {
  const location = useLocation();
  const [isOpen, setIsOpen] = useState(false);
  
  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleLogout = () => {
    const confirm = window.confirm("Es-tu sûr de vouloir te déconnecter ?");
    if (confirm) {
      localStorage.removeItem("token");
      localStorage.removeItem("roles");
      localStorage.removeItem("consultantInfo"); 
      window.location.href = "/";
    }
  };

  const navLinks = [
    { path: "/adminDashboard", label: "AdminDashboard", icon: <ShieldCheck size={20} /> },
    { path: "/register", label: "Add consultant", icon: <CirclePlus size={20} /> },
    { path: "/Dashboard", label: "Dashboard", icon: <Home size={20} /> },
    { path: "/time-tracking", label: "Time Tracking", icon: <Clock size={20} /> },
    { path: "/expenses", label: "Expenses", icon: <Receipt size={20} /> },
    { path: "/leaves", label: "Leaves", icon: <CalendarDays size={20} /> },
    
  ];
  
  return (
    <>
      {/* Mobile menu button */}
      <button 
        onClick={toggleMenu} 
        className="md:hidden fixed top-4 left-4 z-50 bg-blue-600 text-white p-2 rounded-md shadow-lg hover:bg-blue-700 transition-colors"
        aria-label="Toggle menu"
      >
        {isOpen ? <X size={24} /> : <Menu size={24} />}
      </button>
      
      {/* NavbarAdmin - changes based on screen size and menu state */}
      <nav className={`
        bg-gradient-to-b from-blue-700 to-blue-600 text-white flex flex-col fixed z-40
        ${isOpen ? 'left-0' : '-left-full'} md:left-0
        top-0 h-screen w-64 transition-all duration-300 shadow-xl
      `}>
        <div className="flex flex-col items-center p-4 border-b border-blue-500 mb-2">
          {/* Logo image */}
          <div className="w-32 h-20 mb-2 flex items-center justify-center">
            <img 
              src="https://mlyogn3x5ncz.i.optimole.com/w:305/h:118/q:mauto/ig:avif/https://mbs-consulting.ch/wp-content/uploads/2023/10/cropped-MBS-Consulting-FSLI.png"
              alt="MBS Logo" 
              className="max-w-full max-h-full"
            />
          </div>
          <h1 className="text-xl font-bold text-center text-white">MBS Consulting</h1>
        </div>
        
        {/* Navigation links */}
        <div className="flex-1 overflow-y-auto px-2 py-4">
          <div className="space-y-1">
            {navLinks.map((link) => (
              <Link 
                key={link.path}
                to={link.path} 
                className={`
                  flex items-center px-3 py-3 rounded-md transition-all
                  ${location.pathname === link.path 
                    ? "bg-blue-800 text-white font-medium shadow-sm" 
                    : "text-blue-100 hover:bg-blue-700"}
                `}
                onClick={() => setIsOpen(false)}
              >
                <span className="mr-3">{link.icon}</span>
                <span>{link.label}</span>
                {location.pathname === link.path && (
                  <ChevronRight className="ml-auto" size={16} />
                )}
              </Link>
            ))}
          </div>
        </div>
        
        {/* Footer with logout button */}
        <div className="p-4 border-t border-blue-500">
          <button 
            onClick={handleLogout} 
            className="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-md flex items-center justify-center transition-colors shadow-sm"
          >
            <LogOut className="mr-2" size={18} />
            Déconnexion
          </button>
        </div>
      </nav>
      
      {/* Overlay for mobile */}
      {isOpen && (
        <div 
          className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
}

export default NavbarAdmin;