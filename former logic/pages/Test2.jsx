import { useState, useEffect } from 'react';
import { Calendar, <PERSON><PERSON>hart, BarChart, Bar, Line, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer } from 'recharts';
import { Download } from 'lucide-react';

function Test2() {
  const [workTimeData, setWorkTimeData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [startDate, setStartDate] = useState('2023-06-15');
  const [endDate, setEndDate] = useState('2025-05-05');
  const [totalHours, setTotalHours] = useState(0);
  const [remoteWorkDays, setRemoteWorkDays] = useState(0);
  const [inOfficeWorkDays, setInOfficeWorkDays] = useState(0);
  const [averageHoursPerDay, setAverageHoursPerDay] = useState(0);
  const [filteredData, setFilteredData] = useState([]);
  const [view, setView] = useState('table'); // 'table', 'chart', 'weekly'

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      // Dans un environnement réel, ce serait un appel à l'API
      // Pour cette démo, nous simulerons des données
      
      // Simulation de l'appel API
       const response = await fetch(`https://staging.api.erp.fsli-group.com/api/v1/work-time/consultant/?start=${startDate}&end=${endDate}`);
       const data = await response.json();
      
      // Données simulées pour la démonstration
      const simulatedData = generateSimulatedData(startDate, endDate);
      setWorkTimeData(simulatedData);
      
      // Calculs des métriques
      calculateMetrics(simulatedData);
      setFilteredData(simulatedData);
      
    } catch (err) {
      setError("Erreur lors de la récupération des données. Veuillez réessayer.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  console.log(localStorage)

  const generateSimulatedData = (start, end) => {
    const startDateObj = new Date(start);
    const endDateObj = new Date(end);
    const data = [];
    
    const activities = ["Développement", "Conception", "Réunion", "Formation", "Support", "Documentation"];
    
    let currentDate = new Date(startDateObj);
    while (currentDate <= endDateObj) {
      // Ne pas ajouter les weekends
      if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
        const isRemote = Math.random() > 0.6;
        const hours = Math.floor(Math.random() * 4) + 6; // Entre 6 et 9 heures
        
        data.push({
          id: data.length + 1,
          date: currentDate.toISOString().split('T')[0],
          hours: hours,
          remoteWork: isRemote,
          activity: activities[Math.floor(Math.random() * activities.length)],
          comment: isRemote ? "Travail à distance" : "Travail au bureau"
        });
      }
      
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return data;
  };

  const calculateMetrics = (data) => {
    const totalHrs = data.reduce((sum, item) => sum + item.hours, 0);
    const remoteDays = data.filter(item => item.remoteWork).length;
    const officeDays = data.filter(item => !item.remoteWork).length;
    const avgHours = data.length > 0 ? totalHrs / data.length : 0;
    
    setTotalHours(totalHrs);
    setRemoteWorkDays(remoteDays);
    setInOfficeWorkDays(officeDays);
    setAverageHoursPerDay(avgHours);
  };

  useEffect(() => {
    fetchData();
  }, []); // Charger les données au montage initial

  const handleSearch = (e) => {
    e.preventDefault();
    fetchData();
  };

  const exportToCSV = () => {
    if (workTimeData.length === 0) return;

    const headers = ["ID", "Date", "Heures", "Travail à distance", "Activité", "Commentaire"];
    const csvContent = [
      headers.join(","),
      ...workTimeData.map(row => [
        row.id,
        row.date,
        row.hours,
        row.remoteWork ? "Oui" : "Non",
        row.activity,
        `"${row.comment}"`
      ].join(","))
    ].join("\n");

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `temps-travail-${startDate}-${endDate}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const prepareChartData = () => {
    const chartData = [];
    
    // Regrouper par date
    const groupedByDate = {};
    workTimeData.forEach(item => {
      const date = item.date;
      if (!groupedByDate[date]) {
        groupedByDate[date] = {
          date: date,
          hours: item.hours,
          remoteWork: item.remoteWork ? 'Remote' : 'Office'
        };
      } else {
        groupedByDate[date].hours += item.hours;
      }
    });
    
    // Convertir en tableau pour le graphique
    Object.values(groupedByDate).forEach(item => {
      chartData.push(item);
    });
    
    // Trier par date
    chartData.sort((a, b) => new Date(a.date) - new Date(b.date));
    
    return chartData;
  };

  const prepareWeeklyChartData = () => {
    const weeklyData = [];
    
    // Regrouper par semaine
    const groupedByWeek = {};
    workTimeData.forEach(item => {
      const date = new Date(item.date);
      const year = date.getFullYear();
      const weekNum = getWeekNumber(date);
      const weekKey = `${year}-W${weekNum}`;
      
      if (!groupedByWeek[weekKey]) {
        groupedByWeek[weekKey] = {
          week: weekKey,
          totalHours: item.hours,
          remoteHours: item.remoteWork ? item.hours : 0,
          officeHours: !item.remoteWork ? item.hours : 0,
          count: 1
        };
      } else {
        groupedByWeek[weekKey].totalHours += item.hours;
        if (item.remoteWork) {
          groupedByWeek[weekKey].remoteHours += item.hours;
        } else {
          groupedByWeek[weekKey].officeHours += item.hours;
        }
        groupedByWeek[weekKey].count += 1;
      }
    });
    
    // Convertir en tableau pour le graphique
    Object.values(groupedByWeek).forEach(item => {
      weeklyData.push({
        ...item,
        avgHoursPerDay: item.totalHours / item.count
      });
    });
    
    // Trier par semaine
    weeklyData.sort((a, b) => a.week.localeCompare(b.week));
    
    return weeklyData;
  };

  // Fonction pour obtenir le numéro de semaine
  const getWeekNumber = (date) => {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 4 - (d.getDay() || 7));
    const yearStart = new Date(d.getFullYear(), 0, 1);
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  };

  const renderChart = () => {
    const chartData = prepareChartData();
    
    return (
      <div className="h-96 w-full">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis label={{ value: 'Heures', angle: -90, position: 'insideLeft' }} />
            <Tooltip />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="hours" 
              name="Heures de travail" 
              stroke="#8884d8" 
              activeDot={{ r: 8 }} 
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    );
  };

  const renderWeeklyChart = () => {
    const weeklyData = prepareWeeklyChartData();
    
    return (
      <div className="h-96 w-full">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={weeklyData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="week" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="remoteHours" name="Heures à distance" fill="#8884d8" />
            <Bar dataKey="officeHours" name="Heures au bureau" fill="#82ca9d" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    );
  };

  return (
    <div className="flex flex-col p-6 gap-6 bg-gray-50 min-h-screen">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-4">Dashboard Temps de Travail</h1>
        
        {/* Formulaire de recherche */}
        <form onSubmit={handleSearch} className="flex flex-wrap items-end gap-4 mb-6">
          <div className="flex flex-col">
            <label htmlFor="startDate" className="mb-1 text-sm">Date de début</label>
            <input
              type="date"
              id="startDate"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="border rounded p-2"
            />
          </div>
          
          <div className="flex flex-col">
            <label htmlFor="endDate" className="mb-1 text-sm">Date de fin</label>
            <input
              type="date"
              id="endDate"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="border rounded p-2"
            />
          </div>
          
          <button 
            type="submit"
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Rechercher
          </button>
          
          <button 
            type="button"
            onClick={exportToCSV}
            className="flex items-center bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
          >
            <Download size={16} className="mr-2" />
            Exporter CSV
          </button>
        </form>
        
        {/* Onglets pour changer de vue */}
        <div className="flex border-b mb-4">
          <button 
            className={`px-4 py-2 ${view === 'table' ? 'border-b-2 border-blue-600 font-medium' : ''}`}
            onClick={() => setView('table')}
          >
            Tableau
          </button>
          <button 
            className={`px-4 py-2 ${view === 'chart' ? 'border-b-2 border-blue-600 font-medium' : ''}`}
            onClick={() => setView('chart')}
          >
            Graphique quotidien
          </button>
          <button 
            className={`px-4 py-2 ${view === 'weekly' ? 'border-b-2 border-blue-600 font-medium' : ''}`}
            onClick={() => setView('weekly')}
          >
            Graphique hebdomadaire
          </button>
        </div>
        
        {/* Indicateurs clés */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-blue-800">Total des heures</h3>
            <p className="text-2xl font-bold">{totalHours}h</p>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-green-800">Jours en télétravail</h3>
            <p className="text-2xl font-bold">{remoteWorkDays}</p>
          </div>
          
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-purple-800">Jours au bureau</h3>
            <p className="text-2xl font-bold">{inOfficeWorkDays}</p>
          </div>
          
          <div className="bg-amber-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-amber-800">Moyenne quotidienne</h3>
            <p className="text-2xl font-bold">{averageHoursPerDay.toFixed(1)}h</p>
          </div>
        </div>
        
        {/* Affichage du contenu selon l'onglet sélectionné */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 p-4 rounded-lg text-red-800">{error}</div>
        ) : (
          <>
            {view === 'table' && (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Heures</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Travail à distance</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activité</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Commentaire</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredData.map((item) => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">{item.date}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">{item.hours}h</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${item.remoteWork ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}>
                            {item.remoteWork ? 'Oui' : 'Non'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">{item.activity}</td>
                        <td className="px-6 py-4 text-sm">{item.comment}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            
            {view === 'chart' && renderChart()}
            
            {view === 'weekly' && renderWeeklyChart()}
          </>
        )}
      </div>
    </div>
  );
}
export default Test2;