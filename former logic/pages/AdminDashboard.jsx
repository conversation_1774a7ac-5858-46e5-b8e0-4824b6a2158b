import React, { useEffect, useState } from 'react';
import axios from 'axios';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { FileDown, Loader } from 'lucide-react';

const AdminDashboard = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [startDate, setStartDate] = useState("2024-05-02");
  const [endDate, setEndDate] = useState("2025-05-07");

  const fetchDashboardData = async () => {
    const token = localStorage.getItem('token');

    if (!token) {
      setError("Token manquant.");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(
        `https://staging.api.erp.fsli-group.com/api/v1/dashboard/admin?debut=${startDate}&fin=${endDate}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setData(response.data);
    } catch (err) {
      console.error("Erreur lors de la récupération des données :", err);
      setError("Impossible de récupérer les données.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    fetchDashboardData();
  };

  const exportToPDF = () => {
    if (!data) return;

    const doc = new jsPDF();
    doc.text('Tableau de bord Admin', 14, 15);

    const tableColumn = ["ID", "Nom", "Prénom", "Heures totales", "Télétravail (h)", "Dépenses"];
    const tableRows = [];

    data.consultants.forEach((item) => {
      tableRows.push([
        item.consultant.id,
        item.consultant.lastName,
        item.consultant.firstName,
        item.totalHours || 0,
        item.remoteHours || 0,
        item.totalExpenses || 0,
      ]);
    });

    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 20,
      styles: { fontSize: 10 },
      headStyles: { fillColor: [41, 128, 185] },
    });

    doc.save('dashboard_admin.pdf');
  };

  const exportToExcel = () => {
    if (!data) return;

    const worksheetData = data.consultants.map((item) => ({
      ID: item.consultant.id,
      Nom: item.consultant.lastName,
      Prénom: item.consultant.firstName,
      "Heures totales (h)": item.totalHours || 0,
      "Télétravail (h)": item.remoteHours || 0,
      "Dépenses (FCFA)": item.totalExpenses || 0,
    }));

    const worksheet = XLSX.utils.json_to_sheet(worksheetData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Dashboard Admin");

    XLSX.writeFile(workbook, "dashboard_admin.xlsx");
  };

  return (
    <div className="md:ml-64 p-4 sm:p-6 bg-gray-100 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 sm:p-6 rounded-t-xl shadow-md">
          <h2 className="text-xl sm:text-2xl font-bold mb-1">Tableau de bord Admin</h2>
          <p className="text-blue-100 text-sm">Supervision des consultants</p>
        </div>

        <div className="bg-white p-4 sm:p-6 rounded-b-xl shadow-md mb-6">
          {/* Formulaire pour sélectionner la période */}
          <form onSubmit={handleSubmit} className="mb-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Date de début
                </label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full border border-gray-300 rounded p-2"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Date de fin
                </label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full border border-gray-300 rounded p-2"
                  required
                />
              </div>
            </div>
            <button
              type="submit"
              className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Charger les données
            </button>
          </form>

          {loading ? (
            <div className="flex justify-center items-center py-20">
              <Loader className="animate-spin text-blue-600" size={40} />
            </div>
          ) : error ? (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
              {error}
            </div>
          ) : data ? (
            <>
              {/* Données globales */}
              <div className="mb-6">
                <h3 className="text-xl font-bold mb-4">Données globales</h3>
                <p><strong>Nombre de consultants :</strong> {data.global.consultantCount}</p>
                <p><strong>Heures totales :</strong> {data.global.totalHours} h</p>
                <p><strong>Heures en télétravail :</strong> {data.global.remoteHours} h</p>
                <p><strong>Pourcentage télétravail :</strong> {data.global.remoteWorkPercentage.toFixed(2)}%</p>
                <p><strong>Dépenses totales :</strong> {data.global.totalExpenses} FCFA</p>
              </div>

              {/* Liste des consultants */}
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="py-3 px-4 text-left font-semibold text-gray-600 border-b">ID</th>
                      <th className="py-3 px-4 text-left font-semibold text-gray-600 border-b">Nom</th>
                      <th className="py-3 px-4 text-left font-semibold text-gray-600 border-b">Prénom</th>
                      <th className="py-3 px-4 text-left font-semibold text-gray-600 border-b">Heures totales</th>
                      <th className="py-3 px-4 text-left font-semibold text-gray-600 border-b">Télétravail (h)</th>
                      <th className="py-3 px-4 text-left font-semibold text-gray-600 border-b">Dépenses</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.consultants.map((consultant) => (
                      <tr key={consultant.consultant.id} className="hover:bg-gray-50">
                        <td className="py-3 px-4 border-b">{consultant.consultant.id}</td>
                        <td className="py-3 px-4 border-b">{consultant.consultant.lastName}</td>
                        <td className="py-3 px-4 border-b">{consultant.consultant.firstName}</td>
                        <td className="py-3 px-4 border-b">{consultant.totalHours || 0} h</td>
                        <td className="py-3 px-4 border-b">{consultant.remoteHours || 0} h</td>
                        <td className="py-3 px-4 border-b">{consultant.totalExpenses || 0} FCFA</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Boutons d'exportation */}
              <div className="flex space-x-2 mt-6">
                <button
                  onClick={exportToPDF}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded flex items-center transition-colors"
                >
                  <FileDown className="mr-2" size={18} />
                  Exporter en PDF
                </button>
                <button
                  onClick={exportToExcel}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded flex items-center transition-colors"
                >
                  <FileDown className="mr-2" size={18} />
                  Exporter en Excel
                </button>
              </div>
            </>
          ) : (
            <p className="text-gray-500">Aucune donnée disponible.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
