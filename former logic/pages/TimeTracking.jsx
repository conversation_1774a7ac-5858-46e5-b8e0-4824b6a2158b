import { useState, useEffect } from "react";
import {
  Calendar,
  Clock,
  CheckCircle,
  PlusCircle,
  FileText,
  RefreshCw,
  Save,
  Trash2,
  Edit,
  X,
  ChevronLeft,
  ChevronRight,
  CalendarDays,
  List,
  Eye
} from "lucide-react";
import { addTimeTracking, getConsultantTimeEntries } from '../services/timeService';

function TimeTracking() {
  // Default form data for a single entry
  const defaultFormData = {
    date: "",
    activity: "",
    comment: "",
    hours: "",
    remoteWork: false,
  };

  // State for the current entry being edited
  const [formData, setFormData] = useState(defaultFormData);

  // State for temporary entries (not yet submitted to the server)
  const [pendingEntries, setPendingEntries] = useState([]);

  // State for entries fetched from the server
  const [savedEntries, setSavedEntries] = useState([]);

  // UI states
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [consultantId, setConsultantId] = useState(null);

  // Date selection state
  const today = new Date();
  const [selectedMonth, setSelectedMonth] = useState(today.getMonth());
  const [selectedYear, setSelectedYear] = useState(today.getFullYear());
  const [selectedDay, setSelectedDay] = useState(null);
  const [viewMode, setViewMode] = useState('month'); // 'month' or 'day'
  const [daysWithEntries, setDaysWithEntries] = useState([]);

  // Helper functions for date handling
  const formatDateForDisplay = (dateString) => {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('fr-FR', options);
  };

  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getMonthName = (month) => {
    const date = new Date();
    date.setMonth(month);
    return date.toLocaleString('fr-FR', { month: 'long' });
  };

  // Function to move to previous month
  const goToPreviousMonth = () => {
    if (selectedMonth === 0) {
      setSelectedMonth(11);
      setSelectedYear(selectedYear - 1);
    } else {
      setSelectedMonth(selectedMonth - 1);
    }
    // Reset selected day when changing month
    setSelectedDay(null);
  };

  // Function to move to next month
  const goToNextMonth = () => {
    if (selectedMonth === 11) {
      setSelectedMonth(0);
      setSelectedYear(selectedYear + 1);
    } else {
      setSelectedMonth(selectedMonth + 1);
    }
    // Reset selected day when changing month
    setSelectedDay(null);
  };

  // Function to get day of week (0 = Sunday, 1 = Monday, etc.)
  const getDayOfWeek = (year, month, day) => {
    return new Date(year, month, day).getDay();
  };

  // Function to check if a date has entries
  const hasEntriesOnDate = (year, month, day) => {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    return daysWithEntries.includes(dateStr);
  };

  // Function to select a specific day
  const selectDay = (day) => {
    if (day) {
      const formattedDay = String(day).padStart(2, '0');
      const formattedMonth = String(selectedMonth + 1).padStart(2, '0');
      const dateStr = `${selectedYear}-${formattedMonth}-${formattedDay}`;

      setSelectedDay(day);
      setViewMode('day');

      // Pre-fill the form with the selected date
      setFormData({
        ...defaultFormData,
        date: dateStr
      });
    } else {
      setSelectedDay(null);
      setViewMode('month');
    }
  };

  // Function to toggle between day and month view
  const toggleViewMode = () => {
    if (viewMode === 'day') {
      setViewMode('month');
    } else {
      setViewMode('day');
    }
  };

  // Fetch consultant ID and time entries when component mounts or month/day changes
  useEffect(() => {
    const fetchConsultantData = async () => {
      try {
        // Get consultant info from localStorage
        const consultantInfoStr = localStorage.getItem('consultantInfo');
        if (!consultantInfoStr) {
          setError("Informations du consultant non disponibles");
          return;
        }

        const consultantInfo = JSON.parse(consultantInfoStr);
        setConsultantId(consultantInfo.id);

        // Fetch time entries for this consultant
        await fetchTimeEntries(consultantInfo.id);
      } catch (err) {
        console.error("Error fetching consultant data:", err);
        setError("Erreur lors de la récupération des données");
      }
    };

    fetchConsultantData();
  }, [selectedMonth, selectedYear, selectedDay]); // Re-fetch when month, year, or day changes

  // Function to fetch time entries for the selected month/day
  const fetchTimeEntries = async (id) => {
    setLoading(true);
    setError('');
    try {
      // Get all entries for the consultant
      const data = await getConsultantTimeEntries(id);

      // Filter entries for the selected month
      const filteredEntries = data.filter(entry => {
        const entryDate = new Date(entry.date);
        return entryDate.getMonth() === selectedMonth &&
               entryDate.getFullYear() === selectedYear;
      });

      // Track which days have entries
      const daysSet = new Set();
      filteredEntries.forEach(entry => {
        const day = new Date(entry.date).getDate();
        daysSet.add(day);
      });
      setDaysWithEntries(Array.from(daysSet));

      // Further filter by day if in day view
      let entriesToShow = filteredEntries;
      if (selectedDay) {
        entriesToShow = filteredEntries.filter(entry => {
          const entryDay = new Date(entry.date).getDate();
          return entryDay === selectedDay;
        });
      }

      // Sort entries by date (newest first)
      entriesToShow.sort((a, b) => new Date(b.date) - new Date(a.date));

      setSavedEntries(entriesToShow);
    } catch (err) {
      console.error("Error fetching time entries:", err);
      setError("Erreur lors de la récupération des entrées de temps");
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  // Add current form data to pending entries
  const addEntryToPending = (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.date || !formData.activity || !formData.hours) {
      setError("Veuillez remplir tous les champs obligatoires");
      return;
    }

    // Create a new entry with a temporary ID
    const newEntry = {
      ...formData,
      tempId: Date.now(), // Temporary ID for tracking in the UI
      hours: parseFloat(formData.hours) // Ensure hours is a number
    };

    // Add to pending entries
    setPendingEntries([...pendingEntries, newEntry]);

    // Show success message
    setMessage("Entrée ajoutée à la liste. N'oubliez pas de soumettre vos entrées !");

    // Reset form for next entry
    setFormData(defaultFormData);

    // Clear message after 3 seconds
    setTimeout(() => setMessage(""), 3000);
  };

  // Remove an entry from pending list
  const removePendingEntry = (tempId) => {
    setPendingEntries(pendingEntries.filter(entry => entry.tempId !== tempId));
  };

  // Edit a pending entry
  const editPendingEntry = (entry) => {
    setFormData(entry);
    removePendingEntry(entry.tempId);
  };

  // Submit all pending entries to the server
  const submitAllEntries = async () => {
    if (pendingEntries.length === 0) {
      setError("Aucune entrée à soumettre");
      return;
    }

    if (!consultantId) {
      setError("ID du consultant non disponible");
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Process each entry
      for (const entry of pendingEntries) {
        const dataToSubmit = {
          date: entry.date,
          activity: entry.activity,
          comment: entry.comment || "",
          hours: entry.hours,
          remoteWork: entry.remoteWork,
          consultantId: consultantId
        };

        await addTimeTracking(dataToSubmit);
      }

      // Show success message
      setMessage(`${pendingEntries.length} entrée(s) enregistrée(s) avec succès !`);

      // Clear pending entries
      setPendingEntries([]);

      // Refresh entries list
      await fetchTimeEntries(consultantId);

    } catch (err) {
      console.error(err);
      setError("Erreur lors de l'enregistrement des entrées.");
    } finally {
      setLoading(false);
    }
  };

  // Set date to today and select today in the calendar
  const setToday = () => {
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];

    // Update form with today's date
    setFormData({...formData, date: formattedDate});

    // Update calendar to show current month and select today
    setSelectedMonth(today.getMonth());
    setSelectedYear(today.getFullYear());
    setSelectedDay(today.getDate());
    setViewMode('day');
  };

  const adjustTextareaHeight = (e) => {
    e.target.style.height = "auto";
    e.target.style.height = e.target.scrollHeight + "px";
  };

  return (
    <div className="md:ml-64 p-4 sm:p-6 bg-gray-100 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 sm:p-6 rounded-t-xl shadow-md">
          <div className="flex justify-between items-center">
            <h2 className="text-xl sm:text-2xl font-bold mb-1 flex items-center">
              <Clock className="mr-2" size={24} />
              Time Tracking
            </h2>
            <button
              onClick={() => consultantId && fetchTimeEntries(consultantId)}
              className="p-2 bg-blue-700 hover:bg-blue-800 rounded-full transition-colors"
              title="Rafraîchir les données"
              disabled={loading}
            >
              <RefreshCw size={20} className={loading ? "animate-spin" : ""} />
            </button>
          </div>
          <p className="text-blue-100 text-sm">Enregistrez et suivez vos heures de travail</p>
        </div>

        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
            <p>{error}</p>
          </div>
        )}

        {message && (
          <div className="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
            <p>{message}</p>
          </div>
        )}

        {/* Month selector and calendar */}
        <div className="bg-white p-4 rounded-xl shadow-md mb-6">
          <div className="flex justify-between items-center mb-4">
            <button
              onClick={goToPreviousMonth}
              className="p-2 bg-gray-200 hover:bg-gray-300 rounded-full transition-colors"
            >
              <ChevronLeft size={20} />
            </button>

            <h3 className="text-xl font-semibold text-gray-800">
              {getMonthName(selectedMonth)} {selectedYear}
            </h3>

            <button
              onClick={goToNextMonth}
              className="p-2 bg-gray-200 hover:bg-gray-300 rounded-full transition-colors"
            >
              <ChevronRight size={20} />
            </button>
          </div>

          {/* View mode toggle */}
          <div className="flex justify-end mb-4">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('day')}
                className={`px-3 py-1 rounded-md flex items-center ${
                  viewMode === 'day' ? 'bg-blue-600 text-white' : 'text-gray-700'
                }`}
              >
                <CalendarDays size={16} className="mr-1" />
                Jour
              </button>
              <button
                onClick={() => setViewMode('month')}
                className={`px-3 py-1 rounded-md flex items-center ${
                  viewMode === 'month' ? 'bg-blue-600 text-white' : 'text-gray-700'
                }`}
              >
                <List size={16} className="mr-1" />
                Mois
              </button>
            </div>
          </div>

          {/* Calendar */}
          <div className="mb-2">
            {/* Weekday headers */}
            <div className="grid grid-cols-7 text-center font-medium text-gray-500 mb-2">
              <div>Dim</div>
              <div>Lun</div>
              <div>Mar</div>
              <div>Mer</div>
              <div>Jeu</div>
              <div>Ven</div>
              <div>Sam</div>
            </div>

            {/* Calendar grid */}
            <div className="grid grid-cols-7 gap-1">
              {(() => {
                const days = [];
                const daysInMonth = getDaysInMonth(selectedYear, selectedMonth);
                const firstDayOfMonth = new Date(selectedYear, selectedMonth, 1).getDay();

                // Add empty cells for days before the first day of the month
                for (let i = 0; i < firstDayOfMonth; i++) {
                  days.push(
                    <div key={`empty-${i}`} className="h-10 rounded-md"></div>
                  );
                }

                // Add cells for each day of the month
                for (let day = 1; day <= daysInMonth; day++) {
                  const isToday =
                    day === today.getDate() &&
                    selectedMonth === today.getMonth() &&
                    selectedYear === today.getFullYear();

                  const isSelected = day === selectedDay;

                  const hasEntries = daysWithEntries.includes(day);

                  days.push(
                    <button
                      key={day}
                      onClick={() => selectDay(day)}
                      className={`h-10 rounded-md flex items-center justify-center relative
                        ${isToday ? 'bg-blue-100' : ''}
                        ${isSelected ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'}
                        ${hasEntries && !isSelected ? 'font-bold' : ''}
                      `}
                    >
                      {day}
                      {hasEntries && !isSelected && (
                        <span className="absolute bottom-1 w-1 h-1 bg-blue-600 rounded-full"></span>
                      )}
                    </button>
                  );
                }

                return days;
              })()}
            </div>
          </div>

          {/* Selected day info */}
          {selectedDay && (
            <div className="mt-4 pt-3 border-t border-gray-200">
              <div className="flex justify-between items-center">
                <h4 className="font-medium text-gray-800">
                  {selectedDay} {getMonthName(selectedMonth)} {selectedYear}
                </h4>
                <button
                  onClick={() => setSelectedDay(null)}
                  className="text-blue-600 hover:text-blue-800 flex items-center"
                >
                  <Eye size={16} className="mr-1" />
                  Voir tout le mois
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Form section */}
          <div>
            <form onSubmit={addEntryToPending} className="bg-white p-4 sm:p-6 rounded-xl shadow-md mb-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800 border-b pb-2">
                Ajouter une entrée
              </h3>

              <div className="space-y-4">
                <div className="flex gap-2">
                  <div className="flex-grow">
                    <label className="mb-1 font-medium text-gray-700 flex items-center">
                      <Calendar className="mr-2" size={16} />
                      Date
                    </label>
                    <input
                      type="date"
                      name="date"
                      value={formData.date}
                      onChange={handleChange}
                      className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                      required
                    />
                  </div>
                  <div className="flex items-end">
                    <button
                      type="button"
                      onClick={setToday}
                      className="h-10 px-3 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
                    >
                      Aujourd'hui
                    </button>
                  </div>
                </div>

                <div>
                  <label className="mb-1 font-medium text-gray-700">Activité</label>
                  <input
                    type="text"
                    name="activity"
                    value={formData.activity}
                    onChange={handleChange}
                    placeholder="Ex : Réunion, Développement..."
                    className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                    required
                  />
                </div>

                <div>
                  <label className="mb-1 font-medium text-gray-700 flex items-center">
                    <FileText className="mr-2" size={16} />
                    Commentaire
                  </label>
                  <textarea
                    name="comment"
                    value={formData.comment}
                    onChange={(e) => {
                      handleChange(e);
                      adjustTextareaHeight(e);
                    }}
                    onInput={adjustTextareaHeight}
                    placeholder="Décrivez votre activité en détail..."
                    className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all resize-none overflow-hidden min-h-12"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="mb-1 font-medium text-gray-700 flex items-center">
                    <Clock className="mr-2" size={16} />
                    Heures travaillées
                  </label>
                  <input
                    type="number"
                    name="hours"
                    value={formData.hours}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                    min="0"
                    step="0.5"
                    required
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="remoteWork"
                    name="remoteWork"
                    checked={formData.remoteWork}
                    onChange={handleChange}
                    className="w-5 h-5 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                  />
                  <label htmlFor="remoteWork" className="ml-2 font-medium text-gray-700 flex items-center">
                    <CheckCircle className="mr-2" size={16} />
                    Télétravail
                  </label>
                </div>

                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors w-full flex items-center justify-center"
                >
                  <PlusCircle className="mr-2" size={18} />
                  Ajouter à la liste
                </button>
              </div>
            </form>

            {/* Pending entries section */}
            {pendingEntries.length > 0 && (
              <div className="bg-white p-4 sm:p-6 rounded-xl shadow-md mb-6">
                <h3 className="text-lg font-semibold mb-4 text-gray-800 border-b pb-2 flex justify-between items-center">
                  <span>Entrées en attente ({pendingEntries.length})</span>
                </h3>

                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {pendingEntries.map((entry) => (
                    <div
                      key={entry.tempId}
                      className="border border-gray-200 p-3 rounded-lg bg-yellow-50"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div className="font-medium">{entry.activity}</div>
                        <div className="flex space-x-1">
                          <button
                            onClick={() => editPendingEntry(entry)}
                            className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                            title="Modifier"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => removePendingEntry(entry.tempId)}
                            className="p-1 text-red-600 hover:bg-red-100 rounded"
                            title="Supprimer"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-1 text-sm text-gray-600">
                        <div className="flex items-center">
                          <Calendar className="mr-1 text-blue-600" size={14} />
                          {entry.date}
                        </div>
                        <div className="flex items-center">
                          <Clock className="mr-1 text-blue-600" size={14} />
                          {entry.hours}h
                        </div>
                        {entry.comment && (
                          <div className="col-span-2 mt-1 italic">
                            "{entry.comment.substring(0, 50)}{entry.comment.length > 50 ? '...' : ''}"
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                <button
                  onClick={submitAllEntries}
                  disabled={loading}
                  className="mt-4 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors w-full flex items-center justify-center"
                >
                  {loading ? (
                    <>
                      <RefreshCw className="mr-2 animate-spin" size={18} />
                      Enregistrement...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2" size={18} />
                      Enregistrer toutes les entrées
                    </>
                  )}
                </button>
              </div>
            )}
          </div>

          {/* Saved entries section */}
          <div className="bg-white p-4 sm:p-6 rounded-xl shadow-md">
            <h3 className="text-lg sm:text-xl font-semibold mb-4 text-gray-800 border-b pb-2 flex justify-between items-center">
              <span>
                {selectedDay
                  ? `Entrées du ${selectedDay} ${getMonthName(selectedMonth)}`
                  : `Entrées de ${getMonthName(selectedMonth)} ${selectedYear}`}
              </span>
              {loading && <RefreshCw className="animate-spin text-blue-600" size={20} />}
            </h3>

            {savedEntries.length === 0 ? (
              <div className="text-center py-6 text-gray-500">
                {loading
                  ? "Chargement des données..."
                  : selectedDay
                    ? `Aucune entrée de temps trouvée pour le ${selectedDay} ${getMonthName(selectedMonth)}`
                    : `Aucune entrée de temps trouvée pour ${getMonthName(selectedMonth)} ${selectedYear}`
                }
              </div>
            ) : (
              <div className="space-y-6 max-h-[calc(100vh-300px)] overflow-y-auto">
                {/* Group entries by date */}
                {Object.entries(
                  savedEntries.reduce((groups, entry) => {
                    const date = entry.date;
                    if (!groups[date]) {
                      groups[date] = [];
                    }
                    groups[date].push(entry);
                    return groups;
                  }, {})
                )
                  .sort(([dateA], [dateB]) => new Date(dateB) - new Date(dateA))
                  .map(([date, dayEntries]) => (
                    <div key={date} className="border border-gray-200 p-4 rounded-lg bg-gray-50">
                      <div className="font-semibold text-gray-800 mb-3 flex items-center border-b pb-2">
                        <Calendar className="mr-2 text-blue-600" size={16} />
                        {formatDateForDisplay(date)}
                        <span className="ml-auto text-sm font-normal text-gray-600">
                          {dayEntries.reduce((sum, entry) => sum + entry.hours, 0)}h au total
                        </span>
                      </div>

                      <div className="space-y-3">
                        {dayEntries.map((entry) => (
                          <div
                            key={entry.id}
                            className="border-l-4 border-blue-500 pl-3 py-1"
                          >
                            <div className="flex justify-between items-start">
                              <div className="font-medium">{entry.activity}</div>
                              <div className="flex items-center text-gray-700 ml-2">
                                <Clock className="mr-1 text-blue-600" size={14} />
                                <span>{entry.hours}h</span>
                              </div>
                            </div>

                            {entry.comment && (
                              <div className="text-gray-600 text-sm mt-1">
                                {entry.comment}
                              </div>
                            )}

                            <div className="text-xs text-gray-500 mt-1 flex items-center">
                              {entry.remoteWork ? (
                                <span className="flex items-center">
                                  <CheckCircle className="mr-1 text-green-600" size={12} />
                                  Télétravail
                                </span>
                              ) : (
                                <span className="flex items-center">
                                  <CheckCircle className="mr-1 text-blue-600" size={12} />
                                  Présentiel
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default TimeTracking;