import { useState, useEffect } from "react";
import { Calendar, DollarSign, Tag, Receipt, PlusCircle, FileUp, Paperclip, Trash2, Edit, AlertCircle, CheckCircle, Loader } from "lucide-react";
import { getExpenses, createExpense, deleteExpense } from "../services/expenseService";
import axios from "axios";

function Expenses() {
  const [expenseData, setExpenseData] = useState({
    date: "",
    description: "",
    amount: "",
    currency: "EUR",
    category: "",
    receipt: null,
    receiptName: "",
  });

  const [convertedAmount, setConvertedAmount] = useState(null);
  const [exchangeRate, setExchangeRate] = useState(null);
  const [expenses, setExpenses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [consultantId, setConsultantId] = useState(null);

  useEffect(() => {
    // Get consultant info from localStorage
    const consultantInfoStr = localStorage.getItem('consultantInfo');
    console.log("consultantInfoStr: ", consultantInfoStr);
    if (consultantInfoStr) {
      try {
        const consultantInfo = JSON.parse(consultantInfoStr);
        console.log("ConsultantInfo: ", consultantInfo);
        setConsultantId(consultantInfo.id);
      } catch (err) {
        console.error("Error parsing consultant info:", err);
        setError("Erreur lors de la récupération des informations du consultant");
      }
    }

    // Fetch expenses
    fetchExpenses();
  }, []);

  const fetchExpenses = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await getExpenses();
      setExpenses(data);
    } catch (err) {
      console.error("Error fetching expenses:", err);
      setError("Erreur lors de la récupération des dépenses");
    } finally {
      setLoading(false);
    }
  };

  const handleChange = async (e) => {
    const { name, value } = e.target;
    const newData = { ...expenseData, [name]: value };
    setExpenseData(newData);

    // Recalculer taux/conversion si "currency" ou "amount" change
    if (name === "currency" || name === "amount") {
      if (newData.currency !== "CHF" && newData.amount > 0) {
        try {
          const res = await fetch(`https://api.frankfurter.app/latest?amount=${newData.amount}&from=${newData.currency}&to=CHF`);
          const data = await res.json();
          setConvertedAmount(data.rates.CHF.toFixed(2));
          setExchangeRate(data.rates.CHF);
        } catch (error) {
          console.error("Erreur de conversion :", error);
          setConvertedAmount(null);
          setExchangeRate(null);
        }
      } else {
        setConvertedAmount(null);
        setExchangeRate(null);
      }
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setExpenseData({
        ...expenseData,
        receipt: file,
        receiptName: file.name
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Prepare data for API
      const expensePayload = {
        ...expenseData,
        amount: parseFloat(expenseData.amount),
        consultantId: consultantId,
        convertedAmount: convertedAmount ? parseFloat(convertedAmount) : null,
        exchangeRate: exchangeRate ? parseFloat(exchangeRate) : null
      };
      console.log("Expense payload:", expensePayload);

      // Send to API
      const response = await createExpense(expensePayload);

      // Update local state with the new expense
      setExpenses([...expenses, response]);
      setSuccess("Dépense enregistrée avec succès");

      // Reset form
      setExpenseData({
        date: "",
        description: "",
        amount: "",
        currency: "EUR",
        category: "",
        receipt: null,
        receiptName: "",
      });
      setConvertedAmount(null);
      setExchangeRate(null);

      // Reset file input
      const fileInput = document.getElementById('receipt');
      if (fileInput) fileInput.value = '';
    } catch (err) {
      console.error("Error creating expense:", err);
      if (err.response) {
        console.error("Response data:", err.response.data);
        console.error("Response status:", err.response.status);
      }
      setError(err.response?.data?.message || "Erreur lors de l'enregistrement de la dépense");
    } finally {
      setLoading(false);

      // Auto-hide success message after 5 seconds
      if (!error) {
        setTimeout(() => {
          setSuccess(null);
        }, 5000);
      }
    }
  };

  const handleDelete = async (expenseId) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer cette dépense ?")) {
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await deleteExpense(expenseId);

      // Update local state by removing the deleted expense
      setExpenses(expenses.filter(expense => expense.id !== expenseId));
      setSuccess("Dépense supprimée avec succès");

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 5000);
    } catch (err) {
      console.error("Error deleting expense:", err);
      if (err.response) {
        console.error("Response data:", err.response.data);
        console.error("Response status:", err.response.status);
      }
      setError(err.response?.data?.message || "Erreur lors de la suppression de la dépense");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="md:ml-64 p-4 sm:p-6 bg-gray-100 min-h-screen">
      <div className="max-w-xl mx-auto">
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 sm:p-6 rounded-t-xl shadow-md">
          <h2 className="text-xl sm:text-2xl font-bold mb-1 flex items-center">
            <Receipt className="mr-2" size={24} />
            Déclaration de frais
          </h2>
          <p className="text-blue-100 text-sm">Enregistrez et gérez vos dépenses professionnelles</p>
        </div>

        {/* Messages d'erreur et de succès */}
        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded-r" role="alert">
            <div className="flex items-center">
              <AlertCircle className="mr-2" size={20} />
              <p>{error}</p>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded-r" role="alert">
            <div className="flex items-center">
              <CheckCircle className="mr-2" size={20} />
              <p>{success}</p>
            </div>
          </div>
        )}

        {/* Formulaire */}
        <form onSubmit={handleSubmit} className="bg-white p-4 sm:p-6 rounded-b-xl shadow-md mb-6">
          <div className="space-y-4">
            <div>
              <label className="mb-1 font-medium text-gray-700 flex items-center">
                <Calendar className="mr-2" size={16} />
                Date
              </label>
              <input
                type="date"
                name="date"
                value={expenseData.date}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
                required
              />
            </div>

            <div>
              <label className="mb-1 font-medium text-gray-700 block">Description</label>
              <textarea
                name="description"
                value={expenseData.description}
                onChange={(e) => {
                  handleChange(e);
                }}
                placeholder="Ex : Déjeuner client, billet train..."
                className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all resize-none overflow-hidden min-h-12"
                rows={3}
                required
              />
            </div>

            <div>
              <label className="mb-1 font-medium text-gray-700 flex items-center">
                <DollarSign className="mr-2" size={16} />
                Montant
              </label>
              <input
                type="number"
                name="amount"
                value={expenseData.amount}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
                min="0"
                required
              />
              {convertedAmount && (
                <p className="text-sm text-green-600 mt-1">
                  ≈ {convertedAmount} CHF (Taux : {exchangeRate?.toFixed(4)})
                </p>
              )}
            </div>

            <div>
              <label className="mb-1 font-medium text-gray-700 block">Devise</label>
              <select
                name="currency"
                value={expenseData.currency}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
              >
                <option value="EUR">€ EUR</option>
                <option value="CAD">$ CAD</option>
                <option value="USD">$ USD</option>
                <option value="CHF">CHF</option>
              </select>
            </div>

            <div>
              <label className="mb-1 font-medium text-gray-700 flex items-center">
                <Tag className="mr-2" size={16} />
                Catégorie
              </label>
              <select
                name="category"
                value={expenseData.category}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2"
                required
              >
                <option value="">-- Sélectionner --</option>
                <option value="Transport">Transport</option>
                <option value="Hébergement">Hébergement</option>
                <option value="Restauration">Restauration</option>
                <option value="Autre">Autre</option>
              </select>
            </div>

            <div>
              <label className="mb-1 font-medium text-gray-700 flex items-center">
                <FileUp className="mr-2" size={16} />
                Justificatif
              </label>
              <div className="relative">
                <input
                  type="file"
                  id="receipt"
                  name="receipt"
                  onChange={handleFileChange}
                  className="hidden"
                  accept=".pdf,.jpg,.jpeg,.png"
                />
                <div className="flex items-center gap-2">
                  <label
                    htmlFor="receipt"
                    className="cursor-pointer bg-gray-100 border border-gray-300 rounded p-2 hover:bg-gray-200 transition-colors flex items-center"
                  >
                    <Paperclip size={16} className="mr-2" />
                    Choisir un fichier
                  </label>
                  {expenseData.receiptName ? (
                    <span className="text-sm text-gray-700 truncate max-w-xs">
                      {expenseData.receiptName}
                    </span>
                  ) : (
                    <span className="text-sm text-gray-500 italic">Aucun fichier sélectionné</span>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Formats acceptés: PDF, JPG, JPEG, PNG
                </p>
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className={`bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors w-full sm:w-auto flex items-center justify-center ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? (
                <>
                  <Loader className="mr-2 animate-spin" size={18} />
                  Enregistrement...
                </>
              ) : (
                <>
                  <PlusCircle className="mr-2" size={18} />
                  Enregistrer la dépense
                </>
              )}
            </button>
          </div>
        </form>

        {/* Liste des dépenses */}
        {loading && expenses.length === 0 ? (
          <div className="bg-white p-8 rounded-xl shadow-md flex flex-col items-center justify-center">
            <Loader className="text-blue-600 animate-spin mb-4" size={40} />
            <p className="text-gray-600">Chargement des dépenses...</p>
          </div>
        ) : expenses.length > 0 ? (
          <div className="bg-white p-4 sm:p-6 rounded-xl shadow-md">
            <h3 className="text-lg sm:text-xl font-semibold mb-4 text-gray-800 border-b pb-2">
              Dépenses enregistrées
            </h3>
            <div className="space-y-3">
              {expenses.map((exp) => (
                <div
                  key={exp.id}
                  className="border border-gray-200 p-4 rounded-lg bg-gray-50 hover:bg-blue-50 transition-colors"
                >
                  <div className="grid grid-cols-1 gap-2">
                    <div className="flex justify-between items-start">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 flex-grow">
                        <p className="flex items-center text-gray-700">
                          <Calendar className="mr-2 text-blue-600" size={16} />
                          <span className="font-medium">Date:</span>
                          <span className="ml-1">{exp.date}</span>
                        </p>
                        <p className="flex items-center text-gray-700">
                          <DollarSign className="mr-2 text-blue-600" size={16} />
                          <span className="font-medium">Montant:</span>
                          <span className="ml-1">{exp.amount} {exp.currency}</span>
                          {exp.convertedAmount && exp.currency !== "CHF" && (
                            <span className="ml-2 text-sm text-green-600">
                              (≈ {exp.convertedAmount} CHF)
                            </span>
                          )}
                        </p>
                        <p className="flex items-center text-gray-700">
                          <Tag className="mr-2 text-blue-600" size={16} />
                          <span className="font-medium">Catégorie:</span>
                          <span className="ml-1">{exp.category}</span>
                        </p>
                      </div>

                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleDelete(exp.id)}
                          className="text-red-500 hover:text-red-700 transition-colors p-1 rounded hover:bg-red-100"
                          title="Supprimer"
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>
                    </div>

                    <div className="mt-2 pt-2 border-t border-gray-200">
                      <p className="text-gray-700">
                        <span className="font-medium">Description:</span>
                        <span className="ml-1 whitespace-pre-wrap">{exp.description}</span>
                      </p>
                    </div>

                    {exp.receiptUrl && (
                      <div className="mt-2 pt-2 border-t border-gray-200">
                        <p className="flex items-center text-gray-700">
                          <Paperclip className="mr-2 text-blue-600" size={16} />
                          <span className="font-medium">Justificatif:</span>
                          <a
                            href={exp.receiptUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="ml-1 text-blue-600 hover:underline flex items-center"
                          >
                            Voir le justificatif
                            <FileUp size={12} className="ml-1" />
                          </a>
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="bg-white p-8 rounded-xl shadow-md text-center">
            <div className="text-gray-400 mb-4">
              <Receipt size={48} className="mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-700 mb-2">Aucune dépense</h3>
            <p className="text-gray-500">Vous n'avez pas encore enregistré de dépenses.</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default Expenses;