import { useState, useEffect } from "react";
import axios from "axios";
import { Calendar, Clock, Briefcase, Home, CreditCard, User, ChevronRight, BarChart3, ChevronDown } from "lucide-react";
import WorkChart from "../components/WorkChart";

function Dashboard() {
  const [data, setData] = useState(null);
  const [startDate, setStartDate] = useState("2023-06-01");
  const [endDate, setEndDate] = useState("2025-05-08");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [expanded, setExpanded] = useState({
    info: true,
    workTime: true,
    leaves: true,
    expenses: true,
    charts: true
  });

  const toggleSection = (section) => {
    setExpanded({
      ...expanded,
      [section]: !expanded[section]
    });
  };

  const fetchDashboardData = async () => {
    const token = localStorage.getItem("token");
    const consultantInfo = JSON.parse(localStorage.getItem("consultantInfo"));
    const consultantId = consultantInfo?.id;

    if (!token || !consultantId) {
      setError("Consultant ID ou token manquant.");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await axios.get(
        `https://staging.api.erp.fsli-group.com/api/v1/dashboard/consultant/${consultantId}?debut=${startDate}&fin=${endDate}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      console.log("Data:", response.data);
      setData(response.data);
    } catch (err) {
      console.error("Erreur lors de la récupération des données :", err);
      setError("Impossible de récupérer les données.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    fetchDashboardData();
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="bg-white rounded-xl shadow-lg p-6 lg:p-8 ml-0 md:ml-64">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-800">Tableau de bord</h2>
            <div className="bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-medium">
              {new Date().toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' })}
            </div>
          </div>

          {/* Formulaire pour sélectionner la période */}
          <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl shadow-md p-5 mb-8">
            <form onSubmit={handleSubmit} className="flex flex-col md:flex-row items-end space-y-4 md:space-y-0 md:space-x-4">
              <div className="flex-1">
                <label className="block text-white font-medium mb-2">
                  <Calendar size={16} className="inline mr-2" />
                  Date de début
                </label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full border-none rounded-lg p-3 bg-white bg-opacity-90 focus:ring-2 focus:ring-white focus:outline-none"
                  required
                />
              </div>
              <div className="flex-1">
                <label className="block text-white font-medium mb-2">
                  <Calendar size={16} className="inline mr-2" />
                  Date de fin
                </label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full border-none rounded-lg p-3 bg-white bg-opacity-90 focus:ring-2 focus:ring-white focus:outline-none"
                  required
                />
              </div>
              <button
                type="submit"
                className="bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-blue-50 transition duration-300 flex items-center justify-center whitespace-nowrap"
              >
                <ChevronRight size={18} className="mr-1" />
                Actualiser
              </button>
            </form>
          </div>

          {loading && (
            <div className="flex justify-center items-center p-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          )}
          
          {error && (
            <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded">
              <p className="font-medium">Erreur</p>
              <p>{error}</p>
            </div>
          )}

          {data && (
            <>
              {/* Grid layout pour les statistiques principales */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {/* Carte de temps de travail */}
                <div className="bg-white p-5 rounded-xl shadow-md border-l-4 border-blue-500 hover:shadow-lg transition-shadow">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm text-gray-500 font-medium">Heures totales</p>
                      <p className="text-2xl font-bold text-gray-800">{data.workTime.totalHours} h</p>
                    </div>
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Clock size={24} className="text-blue-600" />
                    </div>
                  </div>
                </div>

                {/* Carte de télétravail */}
                <div className="bg-white p-5 rounded-xl shadow-md border-l-4 border-green-500 hover:shadow-lg transition-shadow">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm text-gray-500 font-medium">Télétravail</p>
                      <p className="text-2xl font-bold text-gray-800">{data.workTime.remoteWorkPercentage}%</p>
                    </div>
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Home size={24} className="text-green-600" />
                    </div>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">{data.workTime.remoteHours} heures</p>
                </div>

                {/* Carte de congés */}
                <div className="bg-white p-5 rounded-xl shadow-md border-l-4 border-purple-500 hover:shadow-lg transition-shadow">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm text-gray-500 font-medium">Congés restants</p>
                      <p className="text-2xl font-bold text-gray-800">{data.leaves.remainingDays} jours</p>
                    </div>
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Briefcase size={24} className="text-purple-600" />
                    </div>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">Année {data.leaves.year}</p>
                </div>

                {/* Carte de dépenses */}
                <div className="bg-white p-5 rounded-xl shadow-md border-l-4 border-amber-500 hover:shadow-lg transition-shadow">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm text-gray-500 font-medium">Dépenses totales</p>
                      <p className="text-2xl font-bold text-gray-800">{data.expenses.totalEur} €</p>
                    </div>
                    <div className="p-2 bg-amber-100 rounded-lg">
                      <CreditCard size={24} className="text-amber-600" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Sections détaillées avec accordéon */}
              <div className="space-y-4">
                {/* Informations du consultant */}
                <div className="bg-white rounded-xl shadow-md overflow-hidden">
                  <div 
                    className="p-4 bg-gray-50 flex justify-between items-center cursor-pointer hover:bg-gray-100 transition-colors"
                    onClick={() => toggleSection('info')}
                  >
                    <div className="flex items-center">
                      <div className="p-2 mr-3 bg-blue-100 rounded-lg">
                        <User size={20} className="text-blue-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-800">Informations du consultant</h3>
                    </div>
                    <ChevronDown 
                      size={20} 
                      className={`text-gray-500 transition-transform duration-300 ${expanded.info ? 'transform rotate-180' : ''}`} 
                    />
                  </div>
                  
                  {expanded.info && (
                    <div className="p-5 border-t border-gray-100">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="flex flex-col">
                          <span className="text-sm text-gray-500">Nom complet</span>
                          <span className="text-gray-800 font-medium">{data.consultant.firstName} {data.consultant.lastName}</span>
                        </div>
                        <div className="flex flex-col">
                          <span className="text-sm text-gray-500">Email</span>
                          <span className="text-gray-800 font-medium">{data.consultant.email}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Temps de travail */}
                <div className="bg-white rounded-xl shadow-md overflow-hidden">
                  <div 
                    className="p-4 bg-gray-50 flex justify-between items-center cursor-pointer hover:bg-gray-100 transition-colors"
                    onClick={() => toggleSection('workTime')}
                  >
                    <div className="flex items-center">
                      <div className="p-2 mr-3 bg-blue-100 rounded-lg">
                        <Clock size={20} className="text-blue-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-800">Détails du temps de travail</h3>
                    </div>
                    <ChevronDown 
                      size={20} 
                      className={`text-gray-500 transition-transform duration-300 ${expanded.workTime ? 'transform rotate-180' : ''}`} 
                    />
                  </div>
                  
                  {expanded.workTime && (
                    <div className="p-5 border-t border-gray-100">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-4 bg-gray-50 rounded-lg flex flex-col items-center">
                          <span className="text-sm text-gray-500 mb-1">Heures totales</span>
                          <span className="text-2xl font-bold text-blue-600">{data.workTime.totalHours} h</span>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-lg flex flex-col items-center">
                          <span className="text-sm text-gray-500 mb-1">Heures en télétravail</span>
                          <span className="text-2xl font-bold text-green-600">{data.workTime.remoteHours} h</span>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-lg flex flex-col items-center">
                          <span className="text-sm text-gray-500 mb-1">% télétravail</span>
                          <div className="relative pt-1 w-full">
                            <div className="flex mb-2 items-center justify-between">
                              <div>
                                <span className="text-xl font-semibold inline-block text-green-600">
                                  {data.workTime.remoteWorkPercentage}%
                                </span>
                              </div>
                            </div>
                            <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
                              <div 
                                style={{ width: `${data.workTime.remoteWorkPercentage}%` }} 
                                className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Graphiques */}
                <div className="bg-white rounded-xl shadow-md overflow-hidden">
                  <div 
                    className="p-4 bg-gray-50 flex justify-between items-center cursor-pointer hover:bg-gray-100 transition-colors"
                    onClick={() => toggleSection('charts')}
                  >
                    <div className="flex items-center">
                      <div className="p-2 mr-3 bg-indigo-100 rounded-lg">
                        <BarChart3 size={20} className="text-indigo-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-800">Graphiques de performance</h3>
                    </div>
                    <ChevronDown 
                      size={20} 
                      className={`text-gray-500 transition-transform duration-300 ${expanded.charts ? 'transform rotate-180' : ''}`} 
                    />
                  </div>
                  
                  {expanded.charts && (
                    <div className="p-5 border-t border-gray-100">
                      <div className="flex justify-center">
                        <div className="w-full md:w-4/5 lg:w-3/4 xl:w-2/3">
                          <WorkChart />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Période actuelle */}
              <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <p className="text-sm text-gray-500">
                  <strong>Période actuelle :</strong> {data.period.start} au {data.period.end}
                </p>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default Dashboard;