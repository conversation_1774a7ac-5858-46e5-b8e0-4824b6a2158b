import { useState } from "react";
import { Calendar, Briefcase, PlusCircle, Clock } from "lucide-react";

function Leaves() {
  const [leave, setLeave] = useState({
    start: "",
    end: "",
    type: "",
  });

  const [leavesList, setLeavesList] = useState([]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setLeave({ ...leave, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setLeavesList([...leavesList, leave]);
    setLeave({ start: "", end: "", type: "" });
  };

  return (
    <div className="md:ml-64 p-4 sm:p-6 bg-gray-100 min-h-screen">
      <div className="max-w-xl mx-auto">
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 sm:p-6 rounded-t-xl shadow-md">
          <h2 className="text-xl sm:text-2xl font-bold mb-1 flex items-center">
            <Briefcase className="mr-2" size={24} />
            Déclaration de congés
          </h2>
          <p className="text-blue-100 text-sm">Demandez et suivez vos congés et absences</p>
        </div>

        {/* Formulaire */}
        <form onSubmit={handleSubmit} className="bg-white p-4 sm:p-6 rounded-b-xl shadow-md mb-6">
          <div className="space-y-4">
            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <Calendar className="mr-2" size={16} />
                Date de début
              </label>
              <input
                type="date"
                name="start"
                value={leave.start}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                required
              />
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <Calendar className="mr-2" size={16} />
                Date de fin
              </label>
              <input
                type="date"
                name="end"
                value={leave.end}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                required
              />
            </div>

            <div>
              <label className="block mb-1 font-medium text-gray-700 flex items-center">
                <Clock className="mr-2" size={16} />
                Type de congé
              </label>
              <select
                name="type"
                value={leave.type}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
                required
              >
                <option value="">-- Choisir --</option>
                <option value="CP">Congés Payés</option>
                <option value="RTT">RTT</option>
                <option value="Maladie">Maladie</option>
                <option value="Sans solde">Sans solde</option>
              </select>
            </div>

            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors w-full sm:w-auto flex items-center justify-center"
            >
              <PlusCircle className="mr-2" size={18} />
              Enregistrer le congé
            </button>
          </div>
        </form>

        {/* Liste des congés */}
        {leavesList.length > 0 && (
          <div className="bg-white p-4 sm:p-6 rounded-xl shadow-md">
            <h3 className="text-lg sm:text-xl font-semibold mb-4 text-gray-800 border-b pb-2">
              Historique des congés
            </h3>
            <div className="space-y-3">
              {leavesList.map((item, index) => (
                <div
                  key={index}
                  className="border border-gray-200 p-4 rounded-lg bg-gray-50 hover:bg-blue-50 transition-colors"
                >
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    <p className="flex items-center text-gray-700">
                      <Calendar className="mr-2 text-blue-600" size={16} />
                      <span className="font-medium">Du:</span>
                      <span className="ml-1">{item.start}</span>
                    </p>
                    <p className="flex items-center text-gray-700">
                      <Calendar className="mr-2 text-blue-600" size={16} />
                      <span className="font-medium">Au:</span>
                      <span className="ml-1">{item.end}</span>
                    </p>
                    <p className="flex items-center text-gray-700 sm:col-span-2">
                      <Clock className="mr-2 text-blue-600" size={16} />
                      <span className="font-medium">Type:</span>
                      <span className="ml-1">{item.type}</span>
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Leaves;