import { useState, useEffect } from 'react';
import { Calendar, Clock } from 'lucide-react';

export default function WorkTimeTracker() {
  const [startDate, setStartDate] = useState('2023-06-15');
  const [endDate, setEndDate] = useState('2025-05-05');
  const [workTimeData, setWorkTimeData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [totalHours, setTotalHours] = useState(0);

  const fetchWorkTimeData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Récupérer le token depuis le localStorage
      const token = localStorage.getItem('token');
      
      if (!token) {
        setError('Aucun token trouvé dans le localStorage');
        setLoading(false);
        return;
      }
      
      const url = `https://staging.api.erp.fsli-group.com/api/v1/work-time/consultant/?start=${startDate}&end=${endDate}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Erreur lors de la récupération des données: ${response.status}`);
      }
      
      const data = await response.json();
      setWorkTimeData(data);
      
      // Calculer le total des heures
      const total = data.reduce((sum, item) => sum + item.hours, 0);
      setTotalHours(total);
      
    } catch (err) {
      setError(`Erreur: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Formatter la date en format français
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('fr-FR', options);
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-100 p-6">
      <div className="max-w-4xl mx-auto w-full bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-6">Suivi du temps de travail</h1>
        
        {/* Formulaire de recherche */}
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                Date de début
              </label>
              <div className="relative">
                <input
                  type="date"
                  id="startDate"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                />
                <Calendar className="absolute right-2 top-2 h-5 w-5 text-gray-400" />
              </div>
            </div>
            
            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                Date de fin
              </label>
              <div className="relative">
                <input
                  type="date"
                  id="endDate"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                />
                <Calendar className="absolute right-2 top-2 h-5 w-5 text-gray-400" />
              </div>
            </div>
            
            <div className="flex items-end">
              <button
                onClick={fetchWorkTimeData}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded font-medium transition duration-200"
                disabled={loading}
              >
                {loading ? 'Chargement...' : 'Rechercher'}
              </button>
            </div>
          </div>
        </div>
        
        {/* Affichage des erreurs */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}
        
        {/* Résumé des heures */}
        {workTimeData.length > 0 && (
          <div className="bg-blue-50 p-4 rounded-lg mb-6 flex items-center">
            <Clock className="h-6 w-6 text-blue-600 mr-2" />
            <span className="text-blue-800 font-medium">
              Total des heures travaillées: {totalHours} heures
            </span>
          </div>
        )}
        
        {/* Tableau des résultats */}
        {workTimeData.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead className="bg-gray-100">
                <tr>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-600">Date</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-600">Heures</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-600">Travail à distance</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-600">Activité</th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-600">Commentaire</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {workTimeData.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50">
                    <td className="py-3 px-4 text-sm text-gray-800">{formatDate(item.date)}</td>
                    <td className="py-3 px-4 text-sm text-gray-800 font-medium">{item.hours}</td>
                    <td className="py-3 px-4 text-sm">
                      {item.remoteWork ? (
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Oui</span>
                      ) : (
                        <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs">Non</span>
                      )}
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-800">{item.activity}</td>
                    <td className="py-3 px-4 text-sm text-gray-800">{item.comment}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : !loading && !error && (
          <div className="text-center py-6 text-gray-500">
            Aucune donnée à afficher. Veuillez lancer une recherche.
          </div>
        )}
      </div>
    </div>
  );
}