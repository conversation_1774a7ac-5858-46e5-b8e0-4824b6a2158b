import { createContext, useContext, useEffect, useState } from "react";
import axios from "axios";

const UserSettingsContext = createContext();

export const UserSettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState(null);

  useEffect(() => {
    const fetchSettings = async () => {
      const token = localStorage.getItem("token");
      const userId = localStorage.getItem("userId"); // assure-toi de stocker ça à la connexion

      if (token && userId) {
        try {
          const response = await axios.get(`http://localhost:5000/api/user-settings`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });
          setSettings(response.data);
        } catch (err) {
          console.error("Erreur lors du chargement des paramètres utilisateur", err);
        }
      }
    };

    fetchSettings();
  }, []);

  return (
    <UserSettingsContext.Provider value={settings}>
      {children}
    </UserSettingsContext.Provider>
  );
};

export const useUserSettings = () => useContext(UserSettingsContext);
