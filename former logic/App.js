import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Navbar from "./components/Navbar";
import PrivateRoute from "./components/PrivateRoute";
import AdminRoute from "./components/AdminRoute";

import Dashboard from "./pages/Dashboard";
import Test from "./pages/Test2";
import WorkTimeDashboard from "./pages/WorkTimeDashboard";
import TimeTracking from "./pages/TimeTracking";
import Expenses from "./pages/Expenses";
import Leaves from "./pages/Leaves";
import Admin from "./pages/Admin";
import Compteur from "./pages/Compteur";
import Login from "./pages/Login";
import Register from "./pages/Register";
import AdminDashboard from "./pages/AdminDashboard";
import NavbarAdmin from "./components/NavbarAdmin";
import { UserSettingsProvider } from "./contexts/UserSettingsContext";

function App() {

  const roles = JSON.parse(localStorage.getItem("roles") || "[]");

  const isAdmin = roles.includes("ROLE_ADMIN");
  const isUser = roles.includes("ROLE_USER");
  


  return (
    <UserSettingsProvider>
  <BrowserRouter>
    {isAdmin ? (
      <>
        <NavbarAdmin />
        <Routes>
          <Route path="/adminDashboard" element={<AdminDashboard />} />
          <Route path="/admin" element={<Admin />} />
          <Route path="/register" element={<Register />} />
          <Route path="/Dashboard" element={<Dashboard />} />
          <Route path="/time-tracking" element={<TimeTracking />} />
          <Route path="/expenses" element={<Expenses />} />
          <Route path="/leaves" element={<Leaves />} />
          <Route path="/test" element={<Compteur />} />
        </Routes>
      </>
    ) : isUser ? (
      <>
        <Navbar />
        <Routes>
          <Route path="/Dashboard" element={<Dashboard />} />
          <Route path="/time-tracking" element={<TimeTracking />} />
          <Route path="/expenses" element={<Expenses />} />
          <Route path="/leaves" element={<Leaves />} />
          <Route path="/test" element={<Test />} />
          <Route path="/work-time" element={<WorkTimeDashboard />} />
        </Routes>
      </>
    ) : (
      <Routes>
        <Route path="/" element={<Login />} />
        <Route path="/test" element={<Test />} />
      </Routes>
      
    )}
  </BrowserRouter>
</UserSettingsProvider>

  );
}

export default App;