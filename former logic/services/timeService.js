import axios from '../api/axios';

export const addTimeTracking = async (data) => {
  return await axios.post('/v1/work-time', data);
};

export const getTimeTracking = async () => {
  try {
    const response = await axios.get('/time-tracking');
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getConsultantTimeEntries = async (consultantId) => {
  try {
    const response = await axios.get(
      `/v1/work-time/consultant/${consultantId}`
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};
